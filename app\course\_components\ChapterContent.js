import { SelectedChaptersContext } from '@/context/SelectedChapter';
import React, { useContext, useState } from 'react'
import YouTube from 'react-youtube';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  PlayCircle,
  Pause,
  SkipForward,
  SkipBack,
  BookOpen,
  FileText,
  Download,
  Share,
  Heart,
  MessageCircle,
  Clock,
  CheckCircle,
  Star
} from 'lucide-react';

function ChapterContent({courseInfo}) {
  const {selectedChapters, setSelectedChapters} = useContext(SelectedChaptersContext)
  const [isPlaying, setIsPlaying] = useState(false)
  const [completedSections, setCompletedSections] = useState(new Set())

  const courses = courseInfo?.courses;
  const enrolToCourse = courseInfo?.enrolToCourse;
  const courseContent= courseInfo?.courses?.courseContent;

  const youTubeVideo = courseContent?.[selectedChapters]?.youtubeVideo
  const topic = courseContent?.[selectedChapters]?.courseData?.[0]?.topic;
  const content = courseContent?.[selectedChapters]?.courseData?.[0]?.content;
  const chapterName = courseContent?.[selectedChapters]?.courseData?.[0]?.chapterName;

  const handleSectionComplete = (sectionIndex) => {
    setCompletedSections(prev => new Set([...prev, sectionIndex]))
  }

  const handleNextChapter = () => {
    if (selectedChapters < courseContent?.length - 1) {
      setSelectedChapters(selectedChapters + 1)
    }
  }

  const handlePrevChapter = () => {
    if (selectedChapters > 0) {
      setSelectedChapters(selectedChapters - 1)
    }
  }

  return (
    <div className='flex-1 bg-white'>
      {/* Chapter Header */}
      <div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white p-8'>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-white/20 text-white">
              الفصل {selectedChapters + 1}
            </Badge>
            <span className="text-blue-100">من {courseContent?.length}</span>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
              <Heart className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
              <Share className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <h1 className='text-3xl font-bold mb-2'>
          {chapterName || `الفصل ${selectedChapters + 1}`}
        </h1>

        <div className="flex items-center gap-4 text-blue-100">
          <span className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            45 دقيقة
          </span>
          <span className="flex items-center gap-1">
            <BookOpen className="w-4 h-4" />
            5 مواضيع
          </span>
          <span className="flex items-center gap-1">
            <Star className="w-4 h-4" />
            4.8 تقييم
          </span>
        </div>
      </div>

      {/* Content Area */}
      <div className='p-8'>
        {/* Video Section */}
        {youTubeVideo && youTubeVideo.length > 0 && (
          <div className='mb-8'>
            <h3 className='text-xl font-bold text-gray-800 mb-4 flex items-center gap-2'>
              <PlayCircle className="w-5 h-5 text-blue-600" />
              الفيديوهات التعليمية
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {youTubeVideo?.slice(0, 3).map((video, index) => (
                <div key={index} className="bg-gray-50 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <YouTube
                    videoId={video?.videoId}
                    opts={{
                      height: '200',
                      width: '100%',
                    }}
                    className="w-full"
                  />
                  <div className="p-3">
                    <h4 className="font-medium text-gray-800 text-sm">
                      فيديو {index + 1}: {video?.title || 'شرح الموضوع'}
                    </h4>
                    <p className="text-xs text-gray-600 mt-1">15 دقيقة</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Topic Section */}
        {topic && (
          <div className='mb-8'>
            <h3 className='text-xl font-bold text-gray-800 mb-4 flex items-center gap-2'>
              <FileText className="w-5 h-5 text-green-600" />
              الموضوع الرئيسي
            </h3>
            <div className='bg-blue-50 p-6 rounded-lg border border-blue-200'>
              <h4 className='font-semibold text-blue-800 text-lg'>{topic}</h4>
            </div>
          </div>
        )}

        {/* Content Section */}
        {content && (
          <div className='mb-8'>
            <h3 className='text-xl font-bold text-gray-800 mb-4 flex items-center gap-2'>
              <BookOpen className="w-5 h-5 text-purple-600" />
              المحتوى التفصيلي
            </h3>
            <div className='bg-white p-6 rounded-lg border border-gray-200 shadow-sm'>
              <div
                className="prose prose-lg max-w-none"
                style={{lineHeight:"1.8"}}
                dangerouslySetInnerHTML={{__html: content}}
              />
            </div>
          </div>
        )}

        {/* Interactive Elements */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-green-50 p-6 rounded-lg border border-green-200">
            <h4 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
              <CheckCircle className="w-5 h-5" />
              نقاط التعلم الرئيسية
            </h4>
            <ul className="space-y-2 text-green-700">
              <li className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                فهم المفاهيم الأساسية
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                التطبيق العملي للمعرفة
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                حل المشاكل المتقدمة
              </li>
            </ul>
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
            <h4 className="font-semibold text-yellow-800 mb-3 flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              ملاحظات مهمة
            </h4>
            <p className="text-yellow-700 text-sm">
              تأكد من فهم هذا الفصل جيداً قبل الانتقال للفصل التالي.
              يمكنك العودة ومراجعة المحتوى في أي وقت.
            </p>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handlePrevChapter}
            disabled={selectedChapters === 0}
            className="flex items-center gap-2"
          >
            <SkipBack className="w-4 h-4" />
            الفصل السابق
          </Button>

          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <MessageCircle className="w-4 h-4 mr-2" />
              أسئلة وأجوبة
            </Button>
            <Button
              onClick={() => handleSectionComplete(selectedChapters)}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              إكمال الفصل
            </Button>
          </div>

          <Button
            onClick={handleNextChapter}
            disabled={selectedChapters >= (courseContent?.length - 1)}
            className="flex items-center gap-2"
          >
            الفصل التالي
            <SkipForward className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ChapterContent