"use client"
import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  BookOpen, 
  Star, 
  Users, 
  PlayCircle,
  ChevronRight,
  Award,
  Target,
  CheckCircle
} from "lucide-react"
import Link from 'next/link'

function CoursePreviewModal({ isOpen, onClose, category, course }) {
  const [selectedCourse, setSelectedCourse] = useState(null)

  if (!category && !course) return null

  // If a single course is passed, show course details directly
  if (course && !category) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl">{course.title}</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="flex flex-wrap gap-4">
              <Badge className={getLevelColor(course.level)}>
                {course.level}
              </Badge>
              <span className="flex items-center gap-1 text-sm text-gray-600">
                <BookOpen className="w-4 h-4" />
                {course.chapters} فصل
              </span>
              <span className="flex items-center gap-1 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                {course.duration}
              </span>
              <span className="flex items-center gap-1 text-sm text-gray-600">
                <Star className="w-4 h-4 text-yellow-500" />
                {course.rating} ({course.students} طالب)
              </span>
            </div>

            <p className="text-gray-600 text-lg">{course.description}</p>

            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-600" />
                فصول الدورة
              </h3>
              <div className="space-y-3">
                {course.chapters_list?.map((chapter, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-800">
                        {index + 1}. {chapter.name}
                      </h4>
                      <span className="text-sm text-gray-500">{chapter.duration}</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {chapter.topics?.map((topic, topicIndex) => (
                        <Badge key={topicIndex} variant="outline" className="text-xs">
                          {topic}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Link href="/dashboard" className="flex-1">
                <Button className="w-full">
                  <PlayCircle className="w-4 h-4 mr-2" />
                  ابدأ هذه الدورة
                </Button>
              </Link>
              <Button variant="outline" onClick={onClose}>
                إغلاق
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  const handleCourseSelect = (course) => {
    setSelectedCourse(course)
  }

  const handleBackToCategory = () => {
    setSelectedCourse(null)
  }

  const getLevelColor = (level) => {
    switch (level) {
      case 'مبتدئ': return 'bg-green-100 text-green-800'
      case 'متوسط': return 'bg-yellow-100 text-yellow-800'
      case 'متقدم': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {!selectedCourse ? (
          // Category Overview
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-3 text-2xl">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${category.color} flex items-center justify-center`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                {category.title}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              <p className="text-gray-600 text-lg">{category.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <BookOpen className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-800">{category.courses.length} دورات</h3>
                  <p className="text-sm text-blue-600">متاحة للتعلم</p>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <Clock className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-800">
                    {category.courses.reduce((total, course) => {
                      const hours = parseInt(course.duration.split(' ')[0])
                      return total + hours
                    }, 0)} ساعة
                  </h3>
                  <p className="text-sm text-green-600">إجمالي المحتوى</p>
                </div>
                
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <Award className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-800">شهادة</h3>
                  <p className="text-sm text-purple-600">عند الإكمال</p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-4 text-gray-800">الدورات المتاحة</h3>
                <div className="space-y-3">
                  {category.courses.map((course, index) => (
                    <div
                      key={index}
                      onClick={() => handleCourseSelect(course)}
                      className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-800 group-hover:text-blue-600 transition-colors">
                              {course.name}
                            </h4>
                            <Badge className={getLevelColor(course.level)}>
                              {course.level}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <BookOpen className="w-4 h-4" />
                              {course.chapters} فصل
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="w-4 h-4" />
                              {course.duration}
                            </span>
                          </div>
                        </div>
                        
                        <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Link href="/dashboard" className="flex-1">
                  <Button className="w-full">
                    <PlayCircle className="w-4 h-4 mr-2" />
                    ابدأ التعلم الآن
                  </Button>
                </Link>
                <Button variant="outline" onClick={onClose}>
                  إغلاق
                </Button>
              </div>
            </div>
          </>
        ) : (
          // Course Details
          <>
            <DialogHeader>
              <div className="flex items-center gap-2 mb-2">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={handleBackToCategory}
                  className="p-1"
                >
                  ← العودة
                </Button>
              </div>
              <DialogTitle className="text-2xl">{selectedCourse.name}</DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              <div className="flex flex-wrap gap-4">
                <Badge className={getLevelColor(selectedCourse.level)}>
                  {selectedCourse.level}
                </Badge>
                <span className="flex items-center gap-1 text-sm text-gray-600">
                  <BookOpen className="w-4 h-4" />
                  {selectedCourse.chapters} فصل
                </span>
                <span className="flex items-center gap-1 text-sm text-gray-600">
                  <Clock className="w-4 h-4" />
                  {selectedCourse.duration}
                </span>
                <span className="flex items-center gap-1 text-sm text-gray-600">
                  <Star className="w-4 h-4 text-yellow-500" />
                  4.8 (1,234 تقييم)
                </span>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  ما ستتعلمه في هذه الدورة
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {selectedCourse.topics.map((topic, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-2">معلومات إضافية</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">المستوى:</span>
                    <span className="font-medium mr-2">{selectedCourse.level}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">اللغة:</span>
                    <span className="font-medium mr-2">العربية</span>
                  </div>
                  <div>
                    <span className="text-gray-600">الشهادة:</span>
                    <span className="font-medium mr-2">متاحة</span>
                  </div>
                  <div>
                    <span className="text-gray-600">الوصول:</span>
                    <span className="font-medium mr-2">مدى الحياة</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Link href="/dashboard" className="flex-1">
                  <Button className="w-full">
                    <PlayCircle className="w-4 h-4 mr-2" />
                    ابدأ هذه الدورة
                  </Button>
                </Link>
                <Button variant="outline" onClick={onClose}>
                  إغلاق
                </Button>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default CoursePreviewModal
