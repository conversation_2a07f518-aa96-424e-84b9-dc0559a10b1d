import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import axios from 'axios'
import { Book, Loader2Icon, PlayCircle, Settings, Clock, Users, Star, Eye } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React, { useState } from 'react'
import { toast } from 'sonner'

function CourseCard({course}) {
  const [loading,setLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const onEnrollCourse= async()=>{
    try{
      setLoading(true)
      const result = await axios.post("/api/enroll-course",{
        courseId:course?.cid
      })
      console.log(result.data)
      toast.success("تم التسجيل بنجاح!")
      setLoading(false)
    }catch(e){
      toast.error("حدث خطأ أثناء التسجيل")
      setLoading(false)
      console.log(e)
    }
  }

  const getLevelColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'beginner':
      case 'مبتدئ': return 'bg-green-100 text-green-800'
      case 'intermediate':
      case 'متوسط': return 'bg-yellow-100 text-yellow-800'
      case 'advanced':
      case 'متقدم': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
      {/* Course Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={course?.imageURL || "/api/placeholder/400/250"}
          width={400}
          height={250}
          alt={course?.courseJson?.course?.name || "Course Image"}
          className='w-full h-full object-cover group-hover:scale-105 transition-transform duration-300'
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Level Badge */}
        <div className="absolute top-3 left-3">
          <Badge className={getLevelColor(course?.courseJson?.course?.level)}>
            {course?.courseJson?.course?.level || 'مبتدئ'}
          </Badge>
        </div>

        {/* Preview Button */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button
            variant="secondary"
            size="sm"
            className="bg-white/90 text-gray-800 hover:bg-white"
            onClick={() => setShowPreview(true)}
          >
            <Eye className="w-4 h-4 mr-2" />
            معاينة
          </Button>
        </div>
      </div>

      {/* Course Content */}
      <div className="p-6">
        <div className="mb-3">
          <h3 className="text-xl font-bold text-gray-800 mb-2 line-clamp-2">
            {course?.courseJson?.course?.name}
          </h3>
          <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
            {course?.courseJson?.course?.description}
          </p>
        </div>

        {/* Course Stats */}
        <div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
          <span className="flex items-center gap-1">
            <Book className="w-4 h-4" />
            {course?.courseJson?.course?.chapters} فصل
          </span>
          <span className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            8 ساعات
          </span>
          <span className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            1.2k
          </span>
        </div>

        {/* Rating */}
        <div className="flex items-center gap-2 mb-4">
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="font-semibold text-sm">4.8</span>
          </div>
          <span className="text-gray-500 text-sm">(234 تقييم)</span>
        </div>

        {/* Category Badge */}
        <div className="mb-4">
          <Badge variant="outline" className="text-xs">
            {course?.courseJson?.course?.category || 'تطوير الويب'}
          </Badge>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          {course?.courseContent?.length ? (
            <Link href={"/dashboard/edit-course/"+course?.cid} className="flex-1">
              <Button className="w-full cursor-pointer">
                <Settings className="w-4 h-4 mr-2" />
                بدء التعلم
              </Button>
            </Link>
          ) : (
            <Button
              disabled={loading}
              onClick={onEnrollCourse}
              className="flex-1 cursor-pointer"
            >
              {loading ? (
                <Loader2Icon className='animate-spin w-4 h-4 mr-2'/>
              ) : (
                <PlayCircle className="w-4 h-4 mr-2"/>
              )}
              التسجيل في الدورة
            </Button>
          )}

          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default CourseCard