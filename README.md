# 🤖 AI CourseGen - منصة التعليم الذكية

منصة تعليمية متطورة تستخدم الذكاء الاصطناعي لتوليد دورات مخصصة وتوفير تجربة تعليمية فريدة لكل متعلم.

![AI CourseGen](https://img.shields.io/badge/AI-CourseGen-blue?style=for-the-badge&logo=react)
![Next.js](https://img.shields.io/badge/Next.js-15-black?style=for-the-badge&logo=next.js)
![React](https://img.shields.io/badge/React-19-blue?style=for-the-badge&logo=react)
![Tailwind CSS](https://img.shields.io/badge/Tailwind-CSS-38B2AC?style=for-the-badge&logo=tailwind-css)

## ✨ المميزات الرئيسية

### 🎯 **توليد الدورات بالذكاء الاصطناعي**
- إن<PERSON><PERSON><PERSON> دورات مخصصة باستخدام Google Gemini AI
- توليد محتوى تفاعلي ومتنوع
- إنشاء صور تلقائية للدورات
- تخصيص المحتوى حسب المستوى والفئة

### 🔐 **نظام مصادقة متقدم**
- تسجيل دخول آمن باستخدام Clerk
- حماية الصفحات والمحتوى
- إدارة ملفات المستخدمين
- تتبع التقدم الشخصي

### 📚 **إدارة شاملة للدورات**
- إنشاء وتحرير الدورات
- تنظيم الفصول والمواضيع
- نظام التسجيل في الدورات
- تتبع التقدم والإنجازات

### 🎨 **واجهة مستخدم حديثة**
- تصميم متجاوب وجذاب
- تأثيرات بصرية متقدمة
- نوافذ منبثقة تفاعلية
- تجربة مستخدم سلسة

## 🛠️ التقنيات المستخدمة

### **Frontend**
- **Next.js 15** - إطار عمل React متقدم
- **React 19** - مكتبة واجهة المستخدم
- **Tailwind CSS** - إطار عمل CSS
- **shadcn/ui** - مكونات UI جاهزة
- **Lucide React** - أيقونات حديثة

### **Backend & Database**
- **Neon PostgreSQL** - قاعدة بيانات سحابية
- **Drizzle ORM** - أداة إدارة قاعدة البيانات
- **Next.js API Routes** - واجهات برمجة التطبيقات

### **Authentication**
- **Clerk** - نظام مصادقة شامل

### **AI Integration**
- **Google Gemini AI** - توليد المحتوى
- **Custom Image Generation API** - توليد الصور

## 🚀 البدء السريع

### المتطلبات الأساسية
- Node.js 18+
- npm أو yarn أو pnpm
- حساب Neon Database
- مفتاح Google Gemini AI
- حساب Clerk

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/ai-course-gen.git
cd ai-course-gen
```

### 2. تثبيت التبعيات
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

### 3. إعداد متغيرات البيئة
أنشئ ملف `.env.local` في جذر المشروع:

```env
# Database
DATABASE_URL="your_neon_database_url"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="your_clerk_publishable_key"
CLERK_SECRET_KEY="your_clerk_secret_key"
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"

# AI Integration
GEMINI_API_KEY="your_gemini_api_key"
API_KEY="your_image_generation_api_key"
```

### 4. إعداد قاعدة البيانات
```bash
npm run db:push
# أو
npx drizzle-kit push:pg
```

### 5. تشغيل المشروع
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

افتح [http://localhost:3000](http://localhost:3000) في متصفحك لرؤية النتيجة.

## 📁 هيكل المشروع

```
├── app/                          # تطبيق Next.js
│   ├── (auth)/                   # صفحات المصادقة
│   ├── api/                      # واجهات API
│   ├── dashboard/                # لوحة التحكم
│   ├── course/                   # صفحات الدورات
│   ├── _components/              # مكونات صفحة الهبوط
│   ├── globals.css               # الأنماط العامة
│   ├── layout.js                 # تخطيط التطبيق
│   └── page.js                   # الصفحة الرئيسية
├── components/                   # مكونات UI قابلة للإعادة
│   └── ui/                       # مكونات shadcn/ui
├── config/                       # إعدادات قاعدة البيانات
│   ├── db.js                     # اتصال قاعدة البيانات
│   └── schema.js                 # مخطط قاعدة البيانات
├── context/                      # React Context
├── hooks/                        # Custom Hooks
├── lib/                          # مكتبات مساعدة
└── public/                       # الملفات العامة
```

## 🎯 الميزات التفاعلية

### **صفحة الهبوط المحسنة**
- تصميم متجاوب وجذاب
- أيقونات تفاعلية قابلة للنقر
- نوافذ منبثقة لعرض تفاصيل الدورات
- تأثيرات بصرية متقدمة

### **نوافذ منبثقة ذكية**
- عرض تفاصيل الدورات
- معاينة الفصول والمواضيع
- معلومات المدربين والتقييمات
- أزرار تفاعلية للتسجيل

### **لوحة تحكم متطورة**
- إحصائيات شخصية
- تتبع التقدم
- إدارة الدورات
- ملف شخصي تفاعلي

## 🔧 الأوامر المتاحة

```bash
# تشغيل المشروع في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل المشروع المبني
npm run start

# فحص الكود
npm run lint

# إدارة قاعدة البيانات
npm run db:push      # رفع التغييرات
npm run db:studio    # فتح Drizzle Studio
```

## 🎨 التخصيص

### **الألوان والثيمات**
يمكنك تخصيص الألوان في `app/globals.css`:

```css
:root {
  --primary: your-color;
  --secondary: your-color;
  /* المزيد من المتغيرات */
}
```

### **المكونات**
جميع المكونات قابلة للتخصيص ومتواجدة في:
- `components/ui/` - مكونات أساسية
- `app/_components/` - مكونات مخصصة

## 🚀 النشر

### **Vercel (موصى به)**
```bash
# ربط المشروع بـ Vercel
vercel

# نشر المشروع
vercel --prod
```

### **متطلبات النشر**
- إعداد متغيرات البيئة في منصة النشر
- التأكد من اتصال قاعدة البيانات
- تفعيل خدمات الذكاء الاصطناعي

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: [www.aicourse.com](https://www.aicourse.com)
- **التوثيق**: [docs.aicourse.com](https://docs.aicourse.com)

## 🙏 شكر وتقدير

- [Next.js](https://nextjs.org/) - إطار العمل الأساسي
- [Tailwind CSS](https://tailwindcss.com/) - إطار عمل CSS
- [Clerk](https://clerk.dev/) - نظام المصادقة
- [Google AI](https://ai.google.dev/) - خدمات الذكاء الاصطناعي
- [Neon](https://neon.tech/) - قاعدة البيانات

---

**تم تطوير هذا المشروع بـ ❤️ لتمكين التعليم بقوة الذكاء الاصطناعي**
