import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";
import JSON5 from 'json5';

export async function POST(req){
    const {courseJson,name,courseId} = await  req.json()


const PROMPT = `
You are an expert course content generator.

Given a chapter name and a list of topics, generate high-quality HTML-formatted educational content for each topic.

Output Requirements:
- Return a valid JSON array.
- Each array item must have the following format:
  {
    "chapterName": "<Chapter name>",
    "topic": "<Topic title>",
    "content": "<HTML content as a string>"
  }
- One object per topic.
- Do NOT include explanations, comments, or code block markers (such as \`\`\`json).
- Properly escape special characters for JSON, including newlines (\\n).

Input:
`;
 const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
const promises = courseJson?.allchapters?.map(async(chapter)=>{

    
const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    responseMimeType: 'text/plain',
  };
  const model = 'gemini-2.5-flash-preview-05-20';
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: PROMPT + JSON.stringify(chapter),
        },
      ],
    },
  ];

  const response = await ai.models.generateContent({
    model,
    config,
    contents,
  });

  console.log(response.candidates[0].content.parts[0].text)
  const rowRES = response?.candidates[0]?.content?.parts[0]?.text
  const rowJSON = rowRES.replace('```json','').replace('```','')
  let ResJson;
try {
  ResJson = JSON5.parse(rowJSON);
} catch (e) {
  console.error("❌ JSON parsing error:", e.message);
  console.log("Offending content:\n", rowJSON);
}
  

  return ResJson
    
})

const courseContent = await Promise.all(promises)

return NextResponse.json({courseContent:courseContent,name:name})
}