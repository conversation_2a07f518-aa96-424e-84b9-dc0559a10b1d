import { NextResponse, type NextRequest } from "next/server";
import { db } from "@/lib/db";
import { usersTable } from "@/lib/schema";
import { getSelf } from "@/lib/actions/user";
import { count, ilike, or } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    // 1. التحقق الأمني: هل المستخدم الذي يستدعي الواجهة هو مسؤول؟
    const self = await getSelf();
    if (self?.role !== "admin") {
      // 403 Forbidden: المستخدم مصادق عليه ولكن ليس لديه صلاحية.
      return NextResponse.json(
        { error: "Forbidden: Access denied." },
        { status: 403 }
      );
    }

    // 2. الحصول على متغيرات الترقيم والبحث من الرابط
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const searchQuery = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "name";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const offset = (page - 1) * limit;

    // 3. بناء شرط البحث في قاعدة البيانات
    const whereClause = searchQuery
      ? or(
          ilike(usersTable.name, `%${searchQuery}%`),
          ilike(usersTable.email, `%${searchQuery}%`)
        )
      : undefined;

    // خريطة لربط أسماء الأعمدة من الواجهة الأمامية بأعمدة قاعدة البيانات الفعلية
    const sortableColumns: { [key: string]: any } = {
      name: usersTable.name,
      email: usersTable.email,
      role: usersTable.role,
    };

    const orderByColumn = sortableColumns[sortBy] || usersTable.name;

    // 4. جلب المستخدمين للصفحة الحالية والعدد الإجمالي في نفس الوقت
    const [paginatedUsers, totalResult] = await Promise.all([
      db.query.usersTable.findMany({
        where: whereClause,
        limit: limit,
        offset: offset,
        orderBy: (users, { asc, desc }) => [
          sortOrder === "asc" ? asc(orderByColumn) : desc(orderByColumn),
        ],
      }),
      db.select({ value: count() }).from(usersTable).where(whereClause),
    ]);

    const totalUsers = totalResult[0].value;
    const totalPages = Math.ceil(totalUsers / limit);

    // 5. إرجاع البيانات مع معلومات الترقيم
    return NextResponse.json({ users: paginatedUsers, totalPages }, { status: 200 });
  } catch (error) {
    console.error("Error fetching all users:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}