import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { User<PERSON><PERSON>on } from "@clerk/nextjs";
import Link from "next/link";
import CoursePreviewModal from "./_components/CoursePreviewModal";
import FeaturesSection from "./_components/FeaturesSection";
import InteractiveCourseGrid from "./_components/InteractiveCourseGrid";
import AnimatedBackground from "./_components/AnimatedBackground";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col justify-between relative">
      <AnimatedBackground />
      {/* Navbar */}
      <nav className="flex justify-between items-center px-6 py-4 shadow-lg bg-white/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">AI</span>
          </div>
          <h1 className="text-2xl font-bold text-blue-600">CourseGen</h1>
        </div>

        <div className="hidden md:flex items-center gap-6">
          <a href="#features" className="text-gray-600 hover:text-blue-600 transition-colors">المجالات</a>
          <a href="#about" className="text-gray-600 hover:text-blue-600 transition-colors">حول المنصة</a>
          <a href="#contact" className="text-gray-600 hover:text-blue-600 transition-colors">اتصل بنا</a>
        </div>

        <UserButton />
      </nav>

      {/* Hero Section */}
      <section className="relative text-center py-24 px-4 bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 border border-white rounded-full"></div>
          <div className="absolute top-32 right-20 w-16 h-16 border border-white rounded-full"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 border border-white rounded-full"></div>
          <div className="absolute bottom-32 right-1/3 w-24 h-24 border border-white rounded-full"></div>
        </div>

        <div className="relative z-10">
          <div className="mb-8">
            <Link href="/sign-in">
              <Button className="bg-white text-blue-600 font-semibold px-8 py-3 rounded-full hover:bg-blue-50 transition-all transform hover:scale-105 shadow-lg">
                تسجيل الدخول
              </Button>
            </Link>

            <Link href="/sign-up" className="ml-4">
              <Button variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600 transition-all px-8 py-3 rounded-full transform hover:scale-105">
                إنشاء حساب جديد
              </Button>
            </Link>
          </div>

          <h1 className="text-5xl md:text-6xl font-extrabold mb-6 leading-tight">
            تعلم بذكاء مع
            <span className="block text-yellow-300">الذكاء الاصطناعي</span>
          </h1>

          <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-8 leading-relaxed opacity-90">
            اكتشف إمكاناتك التعليمية. منصتنا تولد دورات مخصصة لك باستخدام الذكاء الاصطناعي — سريع، فعال، وذكي.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/dashboard">
              <Button className="bg-yellow-400 text-blue-900 font-bold px-8 py-4 rounded-full hover:bg-yellow-300 transition-all transform hover:scale-105 shadow-xl text-lg">
                ابدأ رحلتك التعليمية
              </Button>
            </Link>

            <Button variant="ghost" className="text-white border border-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-full transition-all">
              شاهد العرض التوضيحي
            </Button>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-yellow-300">10,000+</h3>
              <p className="text-blue-100">طالب نشط</p>
            </div>
            <div className="text-center">
              <h3 className="text-3xl font-bold text-yellow-300">50+</h3>
              <p className="text-blue-100">دورة تدريبية</p>
            </div>
            <div className="text-center">
              <h3 className="text-3xl font-bold text-yellow-300">95%</h3>
              <p className="text-blue-100">معدل الرضا</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features / Cards */}
      <FeaturesSection />

      {/* Interactive Course Grid */}
      <InteractiveCourseGrid />

      {/* Middle Message */}
      <section className="py-20 text-center px-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-blue-600 mb-6">الذكاء الاصطناعي هو مدربك الجديد</h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto mb-8 leading-relaxed">
            منصتنا لا تقترح المحتوى فقط — بل تفهم وتيرتك وتفضيلاتك وتقدمك. ابدأ رحلتك التعليمية
            مع تكنولوجيا تتكيف معك شخصياً.
          </p>

          <div className="grid md:grid-cols-3 gap-8 mt-12">
            <div className="p-6 bg-white rounded-xl shadow-lg">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">تعلم مخصص</h3>
              <p className="text-gray-600">محتوى مصمم خصيصاً لمستواك وأهدافك</p>
            </div>

            <div className="p-6 bg-white rounded-xl shadow-lg">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">سرعة في التعلم</h3>
              <p className="text-gray-600">تقنيات متقدمة لتسريع عملية التعلم</p>
            </div>

            <div className="p-6 bg-white rounded-xl shadow-lg">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏆</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">نتائج مضمونة</h3>
              <p className="text-gray-600">تتبع تقدمك وحقق أهدافك بفعالية</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="col-span-2">
              <h3 className="text-2xl font-bold mb-4">AI CourseGen</h3>
              <p className="text-blue-100 mb-4 leading-relaxed">
                منصة تعليمية متطورة تستخدم الذكاء الاصطناعي لتوليد دورات مخصصة
                وتوفير تجربة تعليمية فريدة لكل متعلم.
              </p>
              <div className="flex gap-4">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors cursor-pointer">
                  <span className="text-sm">📧</span>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors cursor-pointer">
                  <span className="text-sm">📱</span>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors cursor-pointer">
                  <span className="text-sm">🌐</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">روابط سريعة</h4>
              <ul className="space-y-2 text-blue-100">
                <li><a href="#" className="hover:text-white transition-colors">الدورات</a></li>
                <li><a href="#" className="hover:text-white transition-colors">حول المنصة</a></li>
                <li><a href="#" className="hover:text-white transition-colors">الأسعار</a></li>
                <li><a href="#" className="hover:text-white transition-colors">المدونة</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">الدعم</h4>
              <ul className="space-y-2 text-blue-100">
                <li><a href="#" className="hover:text-white transition-colors">مركز المساعدة</a></li>
                <li><a href="#" className="hover:text-white transition-colors">اتصل بنا</a></li>
                <li><a href="#" className="hover:text-white transition-colors">الشروط والأحكام</a></li>
                <li><a href="#" className="hover:text-white transition-colors">سياسة الخصوصية</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-blue-500 mt-8 pt-8 text-center">
            <p className="text-blue-100">
              © 2025 AI CourseGen. جميع الحقوق محفوظة. تمكين التعليم بقوة الذكاء الاصطناعي.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
