import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { User<PERSON><PERSON>on } from "@clerk/nextjs";
import Link from "next/link";
import CoursePreviewModal from "./_components/CoursePreviewModal";
import FeaturesSection from "./_components/FeaturesSection";
import InteractiveCourseGrid from "./_components/InteractiveCourseGrid";
import AnimatedBackground from "./_components/AnimatedBackground";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col justify-between relative">
      <AnimatedBackground />
      {/* Enhanced Navbar */}
      <nav className="flex justify-between items-center px-6 py-4 shadow-xl bg-white/95 backdrop-blur-md sticky top-0 z-50 border-b border-blue-100">
        <div className="flex items-center gap-3 animate-fade-in">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300">
            <span className="text-white font-bold text-lg">🤖</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI CourseGen
            </h1>
            <p className="text-xs text-gray-500">منصة التعلم الذكية</p>
          </div>
        </div>

        <div className="hidden md:flex items-center gap-8">
          <a href="#features" className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 group">
            المجالات
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full"></span>
          </a>
          <a href="#courses" className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 group">
            الدورات
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full"></span>
          </a>
          <a href="#about" className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 group">
            حول المنصة
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full"></span>
          </a>
          <Link href="/dashboard">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              لوحة التحكم
            </Button>
          </Link>
        </div>

        <div className="flex items-center gap-3">
          <UserButton />
        </div>
      </nav>

      {/* Enhanced Hero Section */}
      <section className="relative text-center py-32 px-4 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 text-white overflow-hidden min-h-screen flex items-center">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-20 h-20 border-2 border-white rounded-full animate-pulse"></div>
          <div className="absolute top-32 right-20 w-16 h-16 border border-white rounded-full floating"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 border border-white rounded-full animate-bounce-gentle"></div>
          <div className="absolute bottom-32 right-1/3 w-24 h-24 border-2 border-white rounded-full floating" style={{animationDelay: '1s'}}></div>

          {/* Additional decorative elements */}
          <div className="absolute top-1/4 left-1/3 w-8 h-8 bg-white/20 rounded-full blur-sm floating" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-3/4 right-1/4 w-6 h-6 bg-yellow-300/30 rounded-full blur-sm animate-pulse"></div>
          <div className="absolute top-1/2 left-1/6 w-4 h-4 bg-pink-300/30 rounded-full blur-sm floating" style={{animationDelay: '3s'}}></div>
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        <div className="relative z-10 max-w-6xl mx-auto">
          {/* Welcome Badge */}
          <div className="mb-8 animate-fade-in">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-6 py-3 mb-6">
              <span className="text-2xl">🚀</span>
              <span className="text-white font-medium">مرحباً بك في مستقبل التعليم</span>
            </div>
          </div>

          <h1 className="text-6xl md:text-7xl lg:text-8xl font-extrabold mb-8 leading-tight animate-slide-up">
            <span className="block bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
              تعلم بذكاء مع
            </span>
            <span className="block text-yellow-300 mt-4 relative">
              الذكاء الاصطناعي
              <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 blur-xl rounded-lg"></div>
            </span>
          </h1>

          <p className="text-xl md:text-2xl lg:text-3xl max-w-4xl mx-auto mb-12 leading-relaxed opacity-95 animate-slide-up" style={{animationDelay: '0.3s'}}>
            🎯 اكتشف إمكاناتك التعليمية مع منصة تولد
            <span className="text-yellow-300 font-semibold"> دورات مخصصة </span>
            لك باستخدام الذكاء الاصطناعي
            <br />
            <span className="text-lg md:text-xl text-blue-100 mt-2 block">
              ⚡ سريع • 🎯 فعال • 🧠 ذكي
            </span>
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-scale-in" style={{animationDelay: '0.6s'}}>
            <Link href="/dashboard">
              <Button className="group bg-gradient-to-r from-yellow-400 to-orange-400 text-blue-900 font-bold px-10 py-5 rounded-full hover:from-yellow-300 hover:to-orange-300 transition-all transform hover:scale-110 shadow-2xl text-xl relative overflow-hidden">
                <span className="relative z-10 flex items-center gap-3">
                  🚀 ابدأ رحلتك التعليمية
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </Link>

            <Link href="/sign-in">
              <Button variant="outline" className="group text-white border-2 border-white hover:bg-white hover:text-blue-600 px-10 py-5 rounded-full transition-all transform hover:scale-105 text-xl backdrop-blur-sm">
                <span className="flex items-center gap-2">
                  🔑 تسجيل الدخول
                </span>
              </Button>
            </Link>

            <Link href="/sign-up">
              <Button className="group bg-white/20 backdrop-blur-sm text-white border border-white/30 hover:bg-white/30 px-8 py-4 rounded-full transition-all transform hover:scale-105">
                <span className="flex items-center gap-2">
                  ✨ إنشاء حساب
                </span>
              </Button>
            </Link>
          </div>

          {/* Enhanced Stats */}
          <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto animate-fade-in" style={{animationDelay: '0.9s'}}>
            <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
              <div className="text-4xl mb-2">👥</div>
              <h3 className="text-3xl font-bold text-yellow-300 mb-1">10,000+</h3>
              <p className="text-blue-100 text-sm">طالب نشط</p>
            </div>
            <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
              <div className="text-4xl mb-2">📚</div>
              <h3 className="text-3xl font-bold text-yellow-300 mb-1">50+</h3>
              <p className="text-blue-100 text-sm">دورة تدريبية</p>
            </div>
            <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
              <div className="text-4xl mb-2">⭐</div>
              <h3 className="text-3xl font-bold text-yellow-300 mb-1">95%</h3>
              <p className="text-blue-100 text-sm">معدل الرضا</p>
            </div>
            <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
              <div className="text-4xl mb-2">🏆</div>
              <h3 className="text-3xl font-bold text-yellow-300 mb-1">24/7</h3>
              <p className="text-blue-100 text-sm">دعم متواصل</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features / Cards */}
      <FeaturesSection />

      {/* Interactive Course Grid */}
      <InteractiveCourseGrid />

      {/* Enhanced Middle Message */}
      <section className="py-24 px-6 bg-gradient-to-br from-white via-blue-50 to-purple-50 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 border border-blue-300 rounded-full"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 border border-purple-300 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-pink-300 rounded-full"></div>
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="text-center mb-16 animate-fade-in">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full mb-6">
              <span className="text-2xl">🤖</span>
              <span className="font-semibold">قوة الذكاء الاصطناعي</span>
            </div>

            <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-8 leading-tight">
              الذكاء الاصطناعي
              <span className="block">مدربك الشخصي</span>
            </h2>

            <p className="text-2xl text-gray-700 max-w-4xl mx-auto mb-12 leading-relaxed">
              🎯 منصتنا لا تقترح المحتوى فقط — بل
              <span className="text-blue-600 font-semibold"> تفهم وتيرتك وتفضيلاتك وتقدمك</span>
              <br />
              <span className="text-lg text-gray-600 mt-2 block">
                ابدأ رحلتك التعليمية مع تكنولوجيا تتكيف معك شخصياً
              </span>
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 animate-slide-up">
            <div className="group p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🎯</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">تعلم مخصص</h3>
              <p className="text-gray-600 leading-relaxed">محتوى مصمم خصيصاً لمستواك وأهدافك مع تتبع ذكي للتقدم</p>
              <div className="mt-4 w-full bg-blue-100 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full w-4/5"></div>
              </div>
            </div>

            <div className="group p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">⚡</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-green-600 transition-colors">سرعة في التعلم</h3>
              <p className="text-gray-600 leading-relaxed">تقنيات متقدمة لتسريع عملية التعلم وتحسين الاستيعاب</p>
              <div className="mt-4 w-full bg-green-100 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full w-5/6"></div>
              </div>
            </div>

            <div className="group p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🏆</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-purple-600 transition-colors">نتائج مضمونة</h3>
              <p className="text-gray-600 leading-relaxed">تتبع تقدمك وحقق أهدافك بفعالية مع شهادات معتمدة</p>
              <div className="mt-4 w-full bg-purple-100 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full w-full"></div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <Link href="/dashboard">
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 rounded-full text-xl font-semibold shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110">
                <span className="flex items-center gap-3">
                  🚀 ابدأ تجربتك المجانية
                </span>
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="col-span-2">
              <h3 className="text-2xl font-bold mb-4">AI CourseGen</h3>
              <p className="text-blue-100 mb-4 leading-relaxed">
                منصة تعليمية متطورة تستخدم الذكاء الاصطناعي لتوليد دورات مخصصة
                وتوفير تجربة تعليمية فريدة لكل متعلم.
              </p>
              <div className="flex gap-4">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors cursor-pointer">
                  <span className="text-sm">📧</span>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors cursor-pointer">
                  <span className="text-sm">📱</span>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-400 transition-colors cursor-pointer">
                  <span className="text-sm">🌐</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">روابط سريعة</h4>
              <ul className="space-y-2 text-blue-100">
                <li><a href="#" className="hover:text-white transition-colors">الدورات</a></li>
                <li><a href="#" className="hover:text-white transition-colors">حول المنصة</a></li>
                <li><a href="#" className="hover:text-white transition-colors">الأسعار</a></li>
                <li><a href="#" className="hover:text-white transition-colors">المدونة</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">الدعم</h4>
              <ul className="space-y-2 text-blue-100">
                <li><a href="#" className="hover:text-white transition-colors">مركز المساعدة</a></li>
                <li><a href="#" className="hover:text-white transition-colors">اتصل بنا</a></li>
                <li><a href="#" className="hover:text-white transition-colors">الشروط والأحكام</a></li>
                <li><a href="#" className="hover:text-white transition-colors">سياسة الخصوصية</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-blue-500 mt-8 pt-8 text-center">
            <p className="text-blue-100">
              © 2025 AI CourseGen. جميع الحقوق محفوظة. تمكين التعليم بقوة الذكاء الاصطناعي.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
