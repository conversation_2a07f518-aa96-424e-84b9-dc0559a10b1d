# 🚀 دليل الإعداد السريع - AI CourseGen

## 📋 المتطلبات الأساسية

قبل البدء، تأكد من توفر:
- **Node.js** (الإصدار 18 أو أحدث)
- **npm** أو **yarn** أو **pnpm**
- **Git** لإدارة الإصدارات
- محرر نصوص (VS Code موصى به)

## 🔧 الإعداد خطوة بخطوة

### الخطوة 1: تحميل المشروع
```bash
# استنساخ المشروع
git clone https://github.com/your-username/ai-course-gen.git

# الانتقال لمجلد المشروع
cd ai-course-gen

# تثبيت التبعيات
npm install
```

### الخطوة 2: إعداد قاعدة البيانات (Neon)

1. **إنشاء حساب في Neon**
   - اذه<PERSON> إلى [neon.tech](https://neon.tech)
   - أنشئ حساب جديد
   - أنشئ مشروع جديد

2. **الحصول على رابط قاعدة البيانات**
   ```
   ************************************************************
   ```

3. **إعداد المخطط**
   ```bash
   npm run db:push
   ```

### الخطوة 3: إعداد المصادقة (Clerk)

1. **إنشاء حساب في Clerk**
   - اذهب إلى [clerk.dev](https://clerk.dev)
   - أنشئ تطبيق جديد
   - اختر "Next.js" كنوع التطبيق

2. **الحصول على المفاتيح**
   - انسخ `Publishable Key`
   - انسخ `Secret Key`

3. **إعداد URLs**
   ```
   Sign-in URL: /sign-in
   Sign-up URL: /sign-up
   After sign-in: /dashboard
   After sign-up: /dashboard
   ```

### الخطوة 4: إعداد الذكاء الاصطناعي

1. **Google Gemini AI**
   - اذهب إلى [Google AI Studio](https://aistudio.google.com)
   - أنشئ مفتاح API جديد
   - انسخ المفتاح

2. **خدمة توليد الصور**
   - احصل على مفتاح API من المزود
   - تأكد من تفعيل الخدمة

### الخطوة 5: إعداد متغيرات البيئة

أنشئ ملف `.env.local` في جذر المشروع:

```env
# قاعدة البيانات
DATABASE_URL="************************************************************"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_..."
CLERK_SECRET_KEY="sk_test_..."
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"

# AI Integration
GEMINI_API_KEY="your_gemini_api_key_here"
API_KEY="your_image_generation_api_key_here"

# اختياري - للتطوير
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### الخطوة 6: تشغيل المشروع

```bash
# تشغيل في وضع التطوير
npm run dev

# أو باستخدام yarn
yarn dev

# أو باستخدام pnpm
pnpm dev
```

افتح [http://localhost:3000](http://localhost:3000) في متصفحك.

## ✅ التحقق من الإعداد

### 1. اختبار قاعدة البيانات
```bash
# فتح Drizzle Studio
npm run db:studio
```

### 2. اختبار المصادقة
- اذهب إلى `/sign-up`
- أنشئ حساب جديد
- تحقق من إعادة التوجيه إلى `/dashboard`

### 3. اختبار الذكاء الاصطناعي
- اذهب إلى لوحة التحكم
- جرب إنشاء دورة جديدة
- تحقق من توليد المحتوى

## 🛠️ أدوات التطوير المفيدة

### VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### إعدادات VS Code
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في قاعدة البيانات
```bash
Error: connection refused
```
**الحل:**
- تحقق من صحة `DATABASE_URL`
- تأكد من تشغيل قاعدة البيانات
- جرب `npm run db:push` مرة أخرى

#### 2. خطأ في المصادقة
```bash
Clerk: Invalid publishable key
```
**الحل:**
- تحقق من صحة مفاتيح Clerk
- تأكد من إعداد URLs بشكل صحيح
- أعد تشغيل الخادم

#### 3. خطأ في الذكاء الاصطناعي
```bash
API key not found
```
**الحل:**
- تحقق من صحة `GEMINI_API_KEY`
- تأكد من تفعيل API في Google Cloud
- تحقق من الحصص المتاحة

#### 4. مشاكل في التصميم
```bash
Tailwind classes not working
```
**الحل:**
- تأكد من تثبيت Tailwind CSS
- تحقق من `tailwind.config.js`
- أعد تشغيل الخادم

## 📚 موارد إضافية

### التوثيق الرسمي
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Clerk Documentation](https://clerk.dev/docs)
- [Drizzle ORM](https://orm.drizzle.team/)

### أمثلة وتطبيقات
- [Next.js Examples](https://github.com/vercel/next.js/tree/canary/examples)
- [Tailwind UI](https://tailwindui.com/)
- [shadcn/ui](https://ui.shadcn.com/)

## 🚀 الخطوات التالية

بعد إكمال الإعداد:

1. **استكشف الكود**
   - راجع هيكل المشروع
   - افهم المكونات الأساسية
   - جرب تعديل التصميم

2. **أضف ميزات جديدة**
   - إنشاء مكونات مخصصة
   - إضافة صفحات جديدة
   - تحسين التفاعل

3. **اختبر وطور**
   - اكتب اختبارات للمكونات
   - حسن الأداء
   - أضف المزيد من التفاعل

4. **انشر المشروع**
   - استخدم Vercel للنشر
   - إعداد النطاق المخصص
   - مراقبة الأداء

## 💡 نصائح للمطورين

### أفضل الممارسات
- استخدم TypeScript للمشاريع الكبيرة
- اكتب كود قابل للقراءة والصيانة
- استخدم Git بشكل منتظم
- اختبر الميزات قبل النشر

### تحسين الأداء
- استخدم `next/image` للصور
- قسم الكود باستخدام `dynamic imports`
- استخدم `React.memo` للمكونات الثقيلة
- حسن استعلامات قاعدة البيانات

---

**مبروك! 🎉 أصبح مشروعك جاهز للتطوير والإبداع!**

للمساعدة أو الأسئلة، لا تتردد في فتح issue في GitHub أو التواصل معنا.
