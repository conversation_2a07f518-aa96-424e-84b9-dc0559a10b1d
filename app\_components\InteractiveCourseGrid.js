"use client"
import React, { useState } from 'react'
import Image from 'next/image'
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  BookOpen, 
  Star, 
  Users, 
  PlayCircle,
  Eye,
  TrendingUp,
  Award
} from "lucide-react"
import Link from 'next/link'
import CoursePreviewModal from './CoursePreviewModal'
import ChapterDetailsSheet from './ChapterDetailsSheet'

const sampleCourses = [
  {
    id: 1,
    title: "تطوير تطبيقات الويب بـ React",
    description: "تعلم بناء تطبيقات ويب حديثة وتفاعلية باستخدام React.js من الصفر حتى الاحتراف",
    image: "/api/placeholder/400/250",
    category: "Web Development",
    level: "متوسط",
    duration: "12 ساعة",
    chapters: 15,
    students: 2340,
    rating: 4.8,
    price: "مجاني",
    instructor: "أحم<PERSON> محمد",
    tags: ["React", "JavaScript", "Frontend"],
    chapters_list: [
      { name: "مقدمة في React", duration: "45 دقيقة", topics: ["JSX", "Components", "Props"] },
      { name: "إدارة الحالة", duration: "60 دقيقة", topics: ["useState", "useEffect", "Context"] },
      { name: "التوجيه والملاحة", duration: "50 دقيقة", topics: ["React Router", "Navigation", "Guards"] }
    ]
  },
  {
    id: 2,
    title: "علم البيانات بـ Python",
    description: "اكتشف عالم البيانات وتعلم كيفية تحليل البيانات وبناء نماذج التعلم الآلي",
    image: "/api/placeholder/400/250",
    category: "Data Science", 
    level: "مبتدئ",
    duration: "18 ساعة",
    chapters: 20,
    students: 1890,
    rating: 4.9,
    price: "99 ريال",
    instructor: "فاطمة أحمد",
    tags: ["Python", "Pandas", "Machine Learning"],
    chapters_list: [
      { name: "أساسيات Python", duration: "60 دقيقة", topics: ["Variables", "Data Types", "Functions"] },
      { name: "مكتبة Pandas", duration: "75 دقيقة", topics: ["DataFrames", "Data Cleaning", "Analysis"] },
      { name: "التصور البياني", duration: "45 دقيقة", topics: ["Matplotlib", "Seaborn", "Plotly"] }
    ]
  },
  {
    id: 3,
    title: "تصميم واجهات المستخدم",
    description: "تعلم أساسيات وأسرار تصميم واجهات مستخدم جذابة وسهلة الاستخدام",
    image: "/api/placeholder/400/250",
    category: "UI/UX Design",
    level: "مبتدئ", 
    duration: "8 ساعات",
    chapters: 12,
    students: 3200,
    rating: 4.7,
    price: "مجاني",
    instructor: "سارة علي",
    tags: ["Figma", "Design", "UX"],
    chapters_list: [
      { name: "مبادئ التصميم", duration: "40 دقيقة", topics: ["Color Theory", "Typography", "Layout"] },
      { name: "أدوات التصميم", duration: "55 دقيقة", topics: ["Figma", "Sketch", "Adobe XD"] },
      { name: "تجربة المستخدم", duration: "50 دقيقة", topics: ["User Research", "Wireframes", "Prototypes"] }
    ]
  },
  {
    id: 4,
    title: "الذكاء الاصطناعي للمبتدئين",
    description: "رحلة شاملة في عالم الذكاء الاصطناعي وتطبيقاته في الحياة العملية",
    image: "/api/placeholder/400/250",
    category: "Artificial Intelligence",
    level: "مبتدئ",
    duration: "15 ساعة",
    chapters: 18,
    students: 1560,
    rating: 4.6,
    price: "149 ريال",
    instructor: "محمد حسن",
    tags: ["AI", "Machine Learning", "Deep Learning"],
    chapters_list: [
      { name: "مقدمة في الذكاء الاصطناعي", duration: "50 دقيقة", topics: ["AI History", "Applications", "Ethics"] },
      { name: "التعلم الآلي", duration: "70 دقيقة", topics: ["Algorithms", "Supervised Learning", "Unsupervised Learning"] },
      { name: "الشبكات العصبية", duration: "65 دقيقة", topics: ["Neural Networks", "Deep Learning", "CNN"] }
    ]
  }
]

function InteractiveCourseGrid() {
  const [selectedCourse, setSelectedCourse] = useState(null)
  const [selectedChapter, setSelectedChapter] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isSheetOpen, setIsSheetOpen] = useState(false)

  const handleCourseClick = (course) => {
    setSelectedCourse(course)
    setIsModalOpen(true)
  }

  const handleChapterClick = (chapter, chapterIndex) => {
    setSelectedChapter({ ...chapter, index: chapterIndex })
    setIsSheetOpen(true)
  }

  const getLevelColor = (level) => {
    switch (level) {
      case 'مبتدئ': return 'bg-green-100 text-green-800'
      case 'متوسط': return 'bg-yellow-100 text-yellow-800'
      case 'متقدم': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
      <section id="courses" className="py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 animate-fade-in">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full mb-6">
              <span className="text-2xl">🔥</span>
              <span className="font-semibold">الدورات الأكثر شعبية</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-800 to-blue-600 bg-clip-text text-transparent mb-6">
              اكتشف عالم التعلم الرقمي
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              🎯 مجموعة مختارة من أفضل الدورات التدريبية المصممة بعناية
              <br />
              <span className="text-blue-600 font-semibold">لتناسب جميع المستويات والاهتمامات</span>
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {sampleCourses.map((course) => (
              <div
                key={course.id}
                className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 group transform hover:scale-105 border border-gray-100"
              >
                {/* Enhanced Course Image */}
                <div className="relative h-56 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 overflow-hidden">
                  {/* Animated Background Pattern */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-4 left-4 w-8 h-8 border border-white rounded-full animate-pulse"></div>
                    <div className="absolute top-8 right-8 w-6 h-6 border border-white rounded-full floating"></div>
                    <div className="absolute bottom-6 left-8 w-4 h-4 border border-white rounded-full animate-bounce-gentle"></div>
                  </div>

                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>

                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex gap-2">
                    <Badge className={`${getLevelColor(course.level)} shadow-lg`}>
                      {course.level}
                    </Badge>
                    <Badge variant="outline" className="bg-white/90 text-gray-800 shadow-lg">
                      {course.price}
                    </Badge>
                  </div>

                  {/* Rating Badge */}
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center gap-1 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full shadow-lg">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-semibold text-gray-800">{course.rating}</span>
                    </div>
                  </div>

                  {/* Preview Button */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Button
                      onClick={() => handleCourseClick(course)}
                      className="bg-white/20 backdrop-blur-md border border-white/30 text-white hover:bg-white/30 transition-all transform hover:scale-110 shadow-xl"
                    >
                      <Eye className="w-5 h-5 mr-2" />
                      معاينة الدورة
                    </Button>
                  </div>

                  {/* Course Category Icon */}
                  <div className="absolute bottom-4 left-4">
                    <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <span className="text-2xl">
                        {course.category === 'Data Science' ? '📊' :
                         course.category === 'UI/UX Design' ? '🎨' :
                         course.category === 'Artificial Intelligence' ? '🤖' : '💻'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Course Content */}
                <div className="p-8">
                  <div className="mb-4">
                    <h3 className="text-2xl font-bold text-gray-800 line-clamp-2 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                      {course.title}
                    </h3>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-sm text-gray-500">بواسطة</span>
                      <span className="text-sm font-semibold text-blue-600">{course.instructor}</span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                    {course.description}
                  </p>

                  {/* Enhanced Course Stats */}
                  <div className="grid grid-cols-3 gap-3 mb-6">
                    <div className="text-center p-3 bg-blue-50 rounded-xl">
                      <Clock className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                      <p className="text-xs text-gray-600">المدة</p>
                      <p className="font-semibold text-sm text-gray-800">{course.duration}</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-xl">
                      <BookOpen className="w-5 h-5 text-green-600 mx-auto mb-1" />
                      <p className="text-xs text-gray-600">الفصول</p>
                      <p className="font-semibold text-sm text-gray-800">{course.chapters} فصل</p>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-xl">
                      <Users className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                      <p className="text-xs text-gray-600">الطلاب</p>
                      <p className="font-semibold text-sm text-gray-800">{course.students.toLocaleString()}</p>
                    </div>
                  </div>

                  {/* Enhanced Rating Section */}
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl mb-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${i < Math.floor(course.rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
                            />
                          ))}
                        </div>
                        <span className="font-bold text-lg text-gray-800">{course.rating}</span>
                      </div>
                      <span className="text-sm text-gray-600 bg-white px-3 py-1 rounded-full">
                        {course.students.toLocaleString()} طالب
                      </span>
                    </div>
                  </div>

                  {/* Enhanced Tags */}
                  <div className="mb-6">
                    <p className="text-sm font-semibold text-gray-700 mb-3">المهارات المكتسبة:</p>
                    <div className="flex flex-wrap gap-2">
                      {course.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 transition-colors"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced Chapters Preview */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                        <BookOpen className="w-4 h-4 text-blue-600" />
                        محتوى الدورة
                      </h4>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        {course.chapters_list.length} فصول
                      </span>
                    </div>
                    <div className="space-y-2">
                      {course.chapters_list.slice(0, 3).map((chapter, index) => (
                        <div
                          key={index}
                          onClick={() => handleChapterClick(chapter, index)}
                          className="group/chapter flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl hover:from-blue-50 hover:to-purple-50 cursor-pointer transition-all duration-300 border border-gray-100 hover:border-blue-200"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 group-hover/chapter:bg-blue-200 rounded-full flex items-center justify-center transition-colors">
                              <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                            </div>
                            <span className="text-sm font-medium text-gray-700 group-hover/chapter:text-blue-700 transition-colors">
                              {chapter.name}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
                              {chapter.duration}
                            </span>
                            <PlayCircle className="w-4 h-4 text-gray-400 group-hover/chapter:text-blue-600 transition-colors" />
                          </div>
                        </div>
                      ))}
                      {course.chapters_list.length > 3 && (
                        <div className="text-center py-2">
                          <span className="text-xs text-gray-500">
                            + {course.chapters_list.length - 3} فصول أخرى
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Enhanced Action Buttons */}
                  <div className="space-y-3">
                    <Button
                      onClick={() => handleCourseClick(course)}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    >
                      <PlayCircle className="w-5 h-5 mr-2" />
                      ابدأ التعلم الآن
                    </Button>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        className="flex-1 border-blue-200 text-blue-600 hover:bg-blue-50 rounded-xl"
                        onClick={() => handleCourseClick(course)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        معاينة
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-gray-200 text-gray-600 hover:bg-gray-50 rounded-xl px-4"
                      >
                        <TrendingUp className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-gray-200 text-gray-600 hover:bg-gray-50 rounded-xl px-4"
                      >
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Modals */}
      <CoursePreviewModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        course={selectedCourse}
      />

      <ChapterDetailsSheet
        chapter={selectedChapter}
        chapterIndex={selectedChapter?.index || 0}
        isOpen={isSheetOpen}
        onOpenChange={setIsSheetOpen}
      />
    </>
  )
}

export default InteractiveCourseGrid
