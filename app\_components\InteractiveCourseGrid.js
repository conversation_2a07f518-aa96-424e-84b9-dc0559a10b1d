"use client"
import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  BookOpen, 
  Star, 
  Users, 
  PlayCircle,
  Eye,
  TrendingUp,
  Award
} from "lucide-react"
import CoursePreviewModal from './CoursePreviewModal'
import ChapterDetailsSheet from './ChapterDetailsSheet'

const sampleCourses = [
  {
    id: 1,
    title: "تطوير تطبيقات الويب بـ React",
    description: "تعلم بناء تطبيقات ويب حديثة وتفاعلية باستخدام React.js من الصفر حتى الاحتراف",
    image: "/api/placeholder/400/250",
    category: "Web Development",
    level: "متوسط",
    duration: "12 ساعة",
    chapters: 15,
    students: 2340,
    rating: 4.8,
    price: "مجاني",
    instructor: "أحم<PERSON> محمد",
    tags: ["React", "JavaScript", "Frontend"],
    chapters_list: [
      { name: "مقدمة في React", duration: "45 دقيقة", topics: ["JSX", "Components", "Props"] },
      { name: "إدارة الحالة", duration: "60 دقيقة", topics: ["useState", "useEffect", "Context"] },
      { name: "التوجيه والملاحة", duration: "50 دقيقة", topics: ["React Router", "Navigation", "Guards"] }
    ]
  },
  {
    id: 2,
    title: "علم البيانات بـ Python",
    description: "اكتشف عالم البيانات وتعلم كيفية تحليل البيانات وبناء نماذج التعلم الآلي",
    image: "/api/placeholder/400/250",
    category: "Data Science", 
    level: "مبتدئ",
    duration: "18 ساعة",
    chapters: 20,
    students: 1890,
    rating: 4.9,
    price: "99 ريال",
    instructor: "فاطمة أحمد",
    tags: ["Python", "Pandas", "Machine Learning"],
    chapters_list: [
      { name: "أساسيات Python", duration: "60 دقيقة", topics: ["Variables", "Data Types", "Functions"] },
      { name: "مكتبة Pandas", duration: "75 دقيقة", topics: ["DataFrames", "Data Cleaning", "Analysis"] },
      { name: "التصور البياني", duration: "45 دقيقة", topics: ["Matplotlib", "Seaborn", "Plotly"] }
    ]
  },
  {
    id: 3,
    title: "تصميم واجهات المستخدم",
    description: "تعلم أساسيات وأسرار تصميم واجهات مستخدم جذابة وسهلة الاستخدام",
    image: "/api/placeholder/400/250",
    category: "UI/UX Design",
    level: "مبتدئ", 
    duration: "8 ساعات",
    chapters: 12,
    students: 3200,
    rating: 4.7,
    price: "مجاني",
    instructor: "سارة علي",
    tags: ["Figma", "Design", "UX"],
    chapters_list: [
      { name: "مبادئ التصميم", duration: "40 دقيقة", topics: ["Color Theory", "Typography", "Layout"] },
      { name: "أدوات التصميم", duration: "55 دقيقة", topics: ["Figma", "Sketch", "Adobe XD"] },
      { name: "تجربة المستخدم", duration: "50 دقيقة", topics: ["User Research", "Wireframes", "Prototypes"] }
    ]
  },
  {
    id: 4,
    title: "الذكاء الاصطناعي للمبتدئين",
    description: "رحلة شاملة في عالم الذكاء الاصطناعي وتطبيقاته في الحياة العملية",
    image: "/api/placeholder/400/250",
    category: "Artificial Intelligence",
    level: "مبتدئ",
    duration: "15 ساعة",
    chapters: 18,
    students: 1560,
    rating: 4.6,
    price: "149 ريال",
    instructor: "محمد حسن",
    tags: ["AI", "Machine Learning", "Deep Learning"],
    chapters_list: [
      { name: "مقدمة في الذكاء الاصطناعي", duration: "50 دقيقة", topics: ["AI History", "Applications", "Ethics"] },
      { name: "التعلم الآلي", duration: "70 دقيقة", topics: ["Algorithms", "Supervised Learning", "Unsupervised Learning"] },
      { name: "الشبكات العصبية", duration: "65 دقيقة", topics: ["Neural Networks", "Deep Learning", "CNN"] }
    ]
  }
]

function InteractiveCourseGrid() {
  const [selectedCourse, setSelectedCourse] = useState(null)
  const [selectedChapter, setSelectedChapter] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isSheetOpen, setIsSheetOpen] = useState(false)

  const handleCourseClick = (course) => {
    setSelectedCourse(course)
    setIsModalOpen(true)
  }

  const handleChapterClick = (chapter, chapterIndex) => {
    setSelectedChapter({ ...chapter, index: chapterIndex })
    setIsSheetOpen(true)
  }

  const getLevelColor = (level) => {
    switch (level) {
      case 'مبتدئ': return 'bg-green-100 text-green-800'
      case 'متوسط': return 'bg-yellow-100 text-yellow-800'
      case 'متقدم': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
      <section className="py-16 px-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">الدورات الأكثر شعبية</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              اكتشف مجموعة مختارة من أفضل الدورات التدريبية المصممة بعناية لتناسب جميع المستويات
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {sampleCourses.map((course) => (
              <div
                key={course.id}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
              >
                {/* Course Image */}
                <div className="relative h-48 bg-gradient-to-r from-blue-400 to-purple-500 overflow-hidden">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute top-4 left-4">
                    <Badge className={getLevelColor(course.level)}>
                      {course.level}
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Badge variant="outline" className="bg-white/90">
                      {course.price}
                    </Badge>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button
                      onClick={() => handleCourseClick(course)}
                      className="bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30 transition-all"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      معاينة الدورة
                    </Button>
                  </div>
                </div>

                {/* Course Content */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-xl font-bold text-gray-800 line-clamp-2 flex-1">
                      {course.title}
                    </h3>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {course.description}
                  </p>

                  {/* Course Stats */}
                  <div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {course.duration}
                    </span>
                    <span className="flex items-center gap-1">
                      <BookOpen className="w-4 h-4" />
                      {course.chapters} فصل
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {course.students.toLocaleString()}
                    </span>
                  </div>

                  {/* Rating and Instructor */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="font-semibold text-sm">{course.rating}</span>
                      <span className="text-gray-500 text-sm">({course.students} طالب)</span>
                    </div>
                    <span className="text-sm text-gray-600">بواسطة {course.instructor}</span>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {course.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  {/* Chapters Preview */}
                  <div className="mb-4">
                    <h4 className="font-semibold text-sm mb-2">عينة من الفصول:</h4>
                    <div className="space-y-1">
                      {course.chapters_list.slice(0, 3).map((chapter, index) => (
                        <div
                          key={index}
                          onClick={() => handleChapterClick(chapter, index)}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors group/chapter"
                        >
                          <span className="text-sm text-gray-700 group-hover/chapter:text-blue-600">
                            {index + 1}. {chapter.name}
                          </span>
                          <span className="text-xs text-gray-500">{chapter.duration}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Button 
                      onClick={() => handleCourseClick(course)}
                      className="flex-1"
                    >
                      <PlayCircle className="w-4 h-4 mr-2" />
                      ابدأ التعلم
                    </Button>
                    <Button variant="outline" size="sm">
                      <TrendingUp className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Modals */}
      <CoursePreviewModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        course={selectedCourse}
      />

      <ChapterDetailsSheet
        chapter={selectedChapter}
        chapterIndex={selectedChapter?.index || 0}
        isOpen={isSheetOpen}
        onOpenChange={setIsSheetOpen}
      />
    </>
  )
}

export default InteractiveCourseGrid
