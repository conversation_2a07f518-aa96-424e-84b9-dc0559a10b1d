import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";
import JSON5 from 'json5';

export async function POST(req){
    const {courseJson,name,courseId} = await  req.json()


const PROMPT = `You are an expert React course content generator.

Based on the given input (including chapter name and list of topics), generate high-quality, educational HTML content for each topic.

 Output Rules:
- Return ONLY a **valid JSON array**.
- Each object must follow this schema:
  {
    "chapterName": "string",
    "topic": "string",
    "content": "string (HTML-formatted content, escaped properly for JSON)"
  }
- Do NOT include code block markers like \`\`\`json or \`\`\`.
- Do NOT include any explanations or comments.
- Escape special characters properly (e.g. newlines as \\n).

 Input Data:
`;
 const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
const promises = courseJson?.allchapters?.map(async(chapter)=>{

    
const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    responseMimeType: 'text/plain',
  };
  const model = 'gemini-2.5-flash-preview-05-20';
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: PROMPT + JSON.stringify(chapter),
        },
      ],
    },
  ];

  const response = await ai.models.generateContent({
    model,
    config,
    contents,
  });

  console.log(response.candidates[0].content.parts[0].text)
  const rowRES = response?.candidates[0]?.content?.parts[0]?.text
  const rowJSON = rowRES.replace('```json','').replace('```','')
  let ResJson;
try {
  ResJson = JSON5.parse(rowJSON);
} catch (e) {
  console.error("❌ JSON parsing error:", e.message);
  console.log("Offending content:\n", rowJSON);
}
  

  return ResJson
    
})

const courseContent = await Promise.all(promises)

return NextResponse.json({courseContent:courseContent,name:name})
}