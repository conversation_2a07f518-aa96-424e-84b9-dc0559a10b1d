"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Line,
  LineChart,
  ResponsiveContainer,
  <PERSON>ltip,
  <PERSON>Axis,
  <PERSON>Axis,
} from "recharts";

interface ChartData {
  date: string;
  count: number;
}

interface UsersChartProps {
  data: ChartData[];
  loading?: boolean;
}

// استخدام React.memo لمنع إعادة التصيير غير الضرورية
export const UsersChart = React.memo(function UsersChart({
  data,
  loading,
}: UsersChartProps) {
  // استخدام useMemo لتجنب إعادة إنشاء كائن النمط في كل مرة
  const tooltipStyle = React.useMemo(
    () => ({
      backgroundColor: "hsl(var(--background))",
      border: "1px solid hsl(var(--border))",
    }),
    []
  );

  if (loading) {
    return (
      <Card className="col-span-1 md:col-span-2 lg:col-span-4">
        <CardHeader>
          <Skeleton className="h-6 w-1/2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[250px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="col-span-1 md:col-span-2 lg:col-span-4">
      <CardHeader>
        <CardTitle>User Growth (Last 30 Days)</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={250}>
          <LineChart data={data}>
            <XAxis
              dataKey="date"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              allowDecimals={false}
            />
            <Tooltip contentStyle={tooltipStyle} />
            <Line type="monotone" dataKey="count" stroke="hsl(var(--primary))" strokeWidth={2} dot={false} />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
});