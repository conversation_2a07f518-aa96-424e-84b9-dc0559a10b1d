{"name": "education-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate:pg", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@clerk/nextjs": "^6.23.2", "@google/genai": "^1.5.1", "@google/generative-ai": "^0.24.1", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "json5": "^2.2.3", "lucide-react": "^0.514.0", "mime": "^4.0.7", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-youtube": "^10.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid4": "^2.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^24.0.1", "drizzle-kit": "^0.31.1", "tailwindcss": "^4.1.11", "tsx": "^4.20.2", "tw-animate-css": "^1.3.4"}}