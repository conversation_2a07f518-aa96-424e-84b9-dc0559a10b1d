import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";
import JSON5 from 'json5';

export async function POST(req){
    const {courseJson,name,courseId} = await  req.json()


const PROMPT = `
You are an expert React course content generator.

Your task is to generate detailed, educational, and HTML-formatted content for each topic provided under the given chapter.

Input Format:
{
  "chapterName": "string",
  "topics": ["Topic 1", "Topic 2", ...]
}

Output Requirements:
- Return a valid JSON array.
- Each array element must follow this schema:
  {
    "topic": "string",
    "content": "string (HTML content)"
  }
- One object per topic. The "topic" value must exactly match the one from input.
- The content must be specific to the topic, not general.
- Do NOT add any explanations, summaries, or comments.
- Do NOT use markdown or wrap the output in \`\`\`.
- Escape all special characters properly for JSON, including newlines (\\n).

Example Output:
[
  {
    "topic": "React.memo and PureComponent",
    "content": "<h2>React.memo and PureComponent</h2>\\n<p>React.memo is a higher-order component...</p>"
  },
  {
    "topic": "useMemo and useCallback",
    "content": "<h2>useMemo and useCallback</h2>\\n<p>These hooks help with...</p>"
  }
]
`;

 const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
const promises = courseJson?.allchapters?.map(async(chapter)=>{
  console.log("🚀 chapter sent to model:\n", JSON.stringify(chapter, null, 2));
console.log("🧾 Full courseJson:", JSON.stringify(courseJson, null, 2));

    
const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    responseMimeType: 'text/plain',
  };
  const model = 'gemini-2.5-flash-preview-05-20';
  const contents = [
    {
      role: 'user',
      parts: [
        {
         text: PROMPT + JSON.stringify({
  courseName: name,
  category: courseJson.course.category,
  chapterName: chapter.chapterName,
  topics: chapter.topics
}),
        },
      ],
    },
  ];

  const response = await ai.models.generateContent({
    model,
    config,
    contents,
  });

  console.log(response.candidates[0].content.parts[0].text)
  const rowRES = response?.candidates[0]?.content?.parts[0]?.text
  const rowJSON = rowRES.replace('```json','').replace('```','')
  let ResJson;
try {
  ResJson = JSON5.parse(rowJSON);
} catch (e) {
  console.error("❌ JSON parsing error:", e.message);
  console.log("Offending content:\n", rowJSON);
}
  

   return ResJson.map((item) => ({
    chapterName: chapter.chapterName,
    topic: item.topic,
    content: item.content,
  }));
    
})

const courseContent = await Promise.all(promises)

return NextResponse.json({courseContent:courseContent,name:name})
}