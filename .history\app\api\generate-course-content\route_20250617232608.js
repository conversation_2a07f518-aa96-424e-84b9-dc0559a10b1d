import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";

export async function POST(req){
    const {courseJson,name,courseId} = await  req.json()


    const PROMPT = `Depends on Chapter name and Topic Generate content for each topic in HTML and give response in JSON format.
Schema: chapterName:<>
topic:<>
content:<>
}
: User Input:`
 const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
const promises = courseJson?.course?.allchapters?.map(async(chapter)=>{

    
const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    responseMimeType: 'text/plain',
  };
  const model = 'gemini-2.5-pro';
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: PROMPT + JSON.stringify(chapter),
        },
      ],
    },
  ];

  const response = await ai.models.generateContent({
    model,
    config,
    contents,
  });

  console.log(response.candidates[0].content.parts[0].text)
  const rowRES = response?.candidates[0]?.content?.parts[0]?.text
  const rowJSON = rowRES.replace('```json','').replace('```','')
  const ResJson = JSON.parse(rowJSON)
  

  return ResJson
    
})

const courseContent = await Promise.all(promises)

return NextResponse.json({courseContent:courseContent,name:name})
}