# 🔧 الدليل التقني - AI CourseGen

## 📋 جدول المحتويات
- [هيكل قاعدة البيانات](#هيكل-قاعدة-البيانات)
- [واجهات API](#واجهات-api)
- [مكونات الواجهة](#مكونات-الواجهة)
- [تكامل الذكاء الاصطناعي](#تكامل-الذكاء-الاصطناعي)
- [نظام المصادقة](#نظام-المصادقة)
- [إدارة الحالة](#إدارة-الحالة)

## 🗄️ هيكل قاعدة البيانات

### جدول المستخدمين (users)
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE
);
```

### جدول الدورات (courses)
```sql
CREATE TABLE courses (
  id SERIAL PRIMARY KEY,
  cid VARCHAR NOT NULL UNIQUE,
  name VARCHAR,
  description VARCHAR,
  chapters INTEGER NOT NULL,
  includeVideo BOOLEAN DEFAULT false,
  level VARCHAR NOT NULL,
  category VARCHAR,
  courseJson JSON,
  courseContent JSON,
  imageURL VARCHAR DEFAULT '',
  email VARCHAR REFERENCES users(email)
);
```

### جدول التسجيل (enrollToCourse)
```sql
CREATE TABLE enrollToCourse (
  id SERIAL PRIMARY KEY,
  cid VARCHAR REFERENCES courses(cid),
  email VARCHAR REFERENCES users(email),
  coursesDone JSON
);
```

## 🔌 واجهات API

### 1. إنشاء دورة جديدة
**Endpoint**: `POST /api/generete-course`

**Request Body**:
```json
{
  "courseId": "unique-course-id",
  "name": "اسم الدورة",
  "description": "وصف الدورة",
  "category": "فئة الدورة",
  "level": "المستوى",
  "chapters": 10,
  "includeVideo": true
}
```

**Response**:
```json
{
  "courseId": "unique-course-id"
}
```

### 2. الحصول على قائمة الدورات
**Endpoint**: `GET /api/courses`

**Response**:
```json
[
  {
    "id": 1,
    "cid": "course-id",
    "name": "اسم الدورة",
    "description": "وصف الدورة",
    "courseJson": {...},
    "imageURL": "url-to-image"
  }
]
```

### 3. التسجيل في دورة
**Endpoint**: `POST /api/enroll-course`

**Request Body**:
```json
{
  "courseId": "course-id"
}
```

### 4. توليد محتوى الدورة
**Endpoint**: `POST /api/generate-course-content`

**Request Body**:
```json
{
  "courseId": "course-id"
}
```

## 🎨 مكونات الواجهة

### المكونات الأساسية

#### 1. CourseCard
```jsx
// app/dashboard/_components/CourseCard.js
function CourseCard({ course }) {
  // عرض بطاقة الدورة مع معلومات أساسية
  // أزرار التسجيل والمعاينة
  // تقييمات ومعلومات إضافية
}
```

#### 2. ChapterListSideBar
```jsx
// app/course/_components/ChapterListSideBar.js
function ChapterListSideBar({ courseInfo }) {
  // عرض قائمة الفصول
  // تتبع التقدم
  // تفاعل مع الفصول
}
```

#### 3. ChapterContent
```jsx
// app/course/_components/ChapterContent.js
function ChapterContent({ courseInfo }) {
  // عرض محتوى الفصل
  // فيديوهات يوتيوب
  // محتوى نصي تفاعلي
}
```

### المكونات التفاعلية

#### 1. CoursePreviewModal
```jsx
// app/_components/CoursePreviewModal.js
function CoursePreviewModal({ isOpen, onClose, category, course }) {
  // نافذة منبثقة لمعاينة الدورة
  // عرض تفاصيل شاملة
  // أزرار تفاعلية
}
```

#### 2. FeaturesSection
```jsx
// app/_components/FeaturesSection.js
function FeaturesSection() {
  // عرض فئات الدورات
  // تفاعل مع الأيقونات
  // إحصائيات المنصة
}
```

#### 3. InteractiveCourseGrid
```jsx
// app/_components/InteractiveCourseGrid.js
function InteractiveCourseGrid() {
  // شبكة الدورات التفاعلية
  // معاينة سريعة
  // تفاصيل الفصول
}
```

## 🤖 تكامل الذكاء الاصطناعي

### Google Gemini AI Integration

```javascript
// config/ai.js
import { GoogleGenAI } from '@google/genai';

const ai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY,
});

const generateCourse = async (courseData) => {
  const prompt = `
    Create a detailed course based on: ${JSON.stringify(courseData)}
    
    Response format:
    {
      "course": {
        "name": "string",
        "description": "string",
        "chapters": "number",
        "allchapters": [...]
      }
    }
  `;
  
  const response = await ai.models.generateContent({
    model: 'gemini-2.5-flash-preview-05-20',
    contents: [{ role: 'user', parts: [{ text: prompt }] }]
  });
  
  return JSON.parse(response.candidates[0].content.parts[0].text);
};
```

### توليد الصور

```javascript
// utils/imageGeneration.js
const generateImage = async (imagePrompt) => {
  const response = await axios.post(
    'https://aigurulab.tech/api/generate-image',
    {
      width: 1024,
      height: 1024,
      input: imagePrompt,
      model: 'sdxl',
      aspectRatio: "16:9"
    },
    {
      headers: {
        'x-api-key': process.env.API_KEY,
        'Content-Type': 'application/json',
      },
    }
  );
  
  return response.data.image;
};
```

## 🔐 نظام المصادقة

### إعداد Clerk

```javascript
// app/layout.js
import { ClerkProvider } from "@clerk/nextjs";

export default function RootLayout({ children }) {
  return (
    <ClerkProvider>
      <html lang="ar">
        <body>{children}</body>
      </html>
    </ClerkProvider>
  );
}
```

### Middleware للحماية

```javascript
// middleware.js
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

const isPublicRoute = createRouteMatcher([
  '/', 
  '/sign-in(.*)',
  '/sign-up(.*)'
]);

export default clerkMiddleware(async (auth, req) => {
  if (!isPublicRoute(req)) {
    await auth.protect();
  }
});
```

## 📊 إدارة الحالة

### React Context

```javascript
// context/SelectedChapter.js
import { createContext, useState } from 'react';

export const SelectedChaptersContext = createContext();

export const SelectedChaptersProvider = ({ children }) => {
  const [selectedChapters, setSelectedChapters] = useState(0);
  
  return (
    <SelectedChaptersContext.Provider value={{
      selectedChapters,
      setSelectedChapters
    }}>
      {children}
    </SelectedChaptersContext.Provider>
  );
};
```

### User Context

```javascript
// context/UserContext.js
import { createContext, useContext } from 'react';
import { useUser } from '@clerk/nextjs';

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const { user, isLoaded } = useUser();
  
  return (
    <UserContext.Provider value={{ user, isLoaded }}>
      {children}
    </UserContext.Provider>
  );
};
```

## 🎨 التصميم والأنماط

### Tailwind CSS Configuration

```javascript
// tailwind.config.js
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'bounce-gentle': 'bounceGentle 2s infinite',
      },
      colors: {
        primary: 'hsl(var(--primary))',
        secondary: 'hsl(var(--secondary))',
      }
    },
  },
  plugins: [],
}
```

### أنماط مخصصة

```css
/* app/globals.css */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
```

## 🔧 أدوات التطوير

### Scripts مفيدة

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "drizzle-kit push:pg",
    "db:studio": "drizzle-kit studio"
  }
}
```

### Environment Variables

```env
# قاعدة البيانات
DATABASE_URL="postgresql://..."

# المصادقة
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_..."
CLERK_SECRET_KEY="sk_..."

# الذكاء الاصطناعي
GEMINI_API_KEY="..."
API_KEY="..."
```

## 🚀 نصائح للتطوير

### 1. إضافة ميزة جديدة
1. إنشاء المكون في المجلد المناسب
2. إضافة الأنماط المطلوبة
3. تحديث واجهات API إذا لزم الأمر
4. اختبار الميزة

### 2. تحسين الأداء
- استخدام `React.memo` للمكونات الثقيلة
- تحسين الصور باستخدام `next/image`
- تقسيم الكود باستخدام `dynamic imports`

### 3. إضافة تأثيرات بصرية
- استخدام الأنماط المخصصة في `globals.css`
- إضافة animations جديدة
- تحسين التفاعل مع المستخدم

---

**هذا الدليل يغطي الجوانب التقنية الأساسية للمشروع. للمزيد من التفاصيل، راجع الكود المصدري.**
