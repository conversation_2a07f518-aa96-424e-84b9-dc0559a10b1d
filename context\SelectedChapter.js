import { createContext, useState } from "react";

export const SelectedChaptersContext = createContext()

export const SelectedChaptersProvider = ({ children }) => {
  const [selectedChapters, setSelectedChapters] = useState(0);

  return (
    <SelectedChaptersContext.Provider value={{
      selectedChapters,
      setSelectedChapters
    }}>
      {children}
    </SelectedChaptersContext.Provider>
  );
};