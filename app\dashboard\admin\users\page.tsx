"use client";

import { useEffect, useState, useMemo } from "react";
import axios from "axios";
import { UserDetail, useUserDetail } from "@/context/UserContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Shield, User, ChevronLeft, ChevronRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

// Hook مخصص لتأخير تنفيذ البحث أثناء الكتابة (Debounce)
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<UserDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userDetail: self } = useUserDetail(); // الحصول على بيانات المسؤول الحالي
  const [updatingUserId, setUpdatingUserId] = useState<number | null>(null);
  
  // حالة جديدة للترقيم والبحث من جانب الخادم
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500); // تأخير 500ms
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: '10', // 10 مستخدمين في كل صفحة
          search: debouncedSearchQuery,
        });
        const response = await axios.get<{
          users: UserDetail[];
          totalPages: number;
        }>(`/api/admin/users?${params.toString()}`);
        
        setUsers(response.data.users);
        setTotalPages(response.data.totalPages);
      } catch (err) {
        setError("Failed to fetch users. You may not have permission.");
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, [currentPage, debouncedSearchQuery]); // إعادة جلب البيانات عند تغيير الصفحة أو البحث

  // إعادة التعيين إلى الصفحة الأولى عند بدء بحث جديد
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery]);

  const handleRoleChange = async (userId: number, clerkId: string, newRole: 'admin' | 'student') => {
    setUpdatingUserId(userId);
    try {
      await axios.patch(`/api/admin/users/${clerkId}`, { role: newRole });
      
      // تحديث حالة الواجهة فورًا
      setUsers(currentUsers =>
        currentUsers.map(u => (u.id === userId ? { ...u, role: newRole } : u))
      );

      toast.success(`User role updated to ${newRole}.`);

    } catch (err) {
      console.error("Failed to update role:", err);
      toast.error("Failed to update user role.");
    } finally {
      setUpdatingUserId(null);
    }
  };

  if (error) {
    return <div className="p-8 text-center text-red-500">{error}</div>;
  }

  return (
    <div className="p-4 sm:p-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Input
          placeholder="Search by name or email..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading
              ? Array.from({ length: 5 }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-48" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-8 float-right" /></TableCell>
                  </TableRow>
                )) // لم نعد نستخدم filteredUsers
              : users.length > 0 ? (
                users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.name || "N/A"}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge variant={user.role === 'admin' ? 'destructive' : 'secondary'}>{user.role}</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {/* لا تعرض قائمة الإجراءات للمسؤول الحالي (لمنع تغيير دوره بنفسه) */}
                      {user.clerkUserId !== self?.clerkUserId && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0" disabled={updatingUserId === user.id} aria-label={`Actions for ${user.name}`}>
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              disabled={user.role === 'admin'}
                              onClick={() => handleRoleChange(user.id, user.clerkUserId, 'admin')}
                            >
                              <Shield className="mr-2 h-4 w-4" />
                              Make Admin
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              disabled={user.role === 'student'}
                              onClick={() => handleRoleChange(user.id, user.clerkUserId, 'student')}
                            >
                              <User className="mr-2 h-4 w-4" />
                              Make Student
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                    No users found matching your search.
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </div>
      {/* أزرار التحكم بالصفحات */}
      <div className="flex items-center justify-end space-x-2 py-4">
        <span className="text-sm text-muted-foreground">
          Page {currentPage} of {totalPages}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage <= 1 || loading}
        >
          <ChevronLeft className="h-4 w-4 mr-1" /> Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
          disabled={currentPage >= totalPages || loading}
        >
          Next <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
}