import { AppSidebar } from "./_components/AppSidebar";
import { Header } from "@/components/Header";
import { SkipLink } from "@/app/components/skip-link";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen">
      <SkipLink />
      <AppSidebar />
      <div className="flex flex-col flex-1">
        <Header />
        <main id="main-content" className="flex-1 overflow-y-auto p-4 sm:p-8">{children}</main>
      </div>
    </div>
  );
}