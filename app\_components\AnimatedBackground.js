"use client"
import React from 'react'

function AnimatedBackground() {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
      
      {/* Floating Shapes */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200/30 rounded-full blur-xl animate-bounce-gentle"></div>
      <div className="absolute top-32 right-20 w-16 h-16 bg-purple-200/30 rounded-full blur-xl floating" style={{animationDelay: '1s'}}></div>
      <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-green-200/30 rounded-full blur-xl floating" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-32 right-1/3 w-24 h-24 bg-yellow-200/30 rounded-full blur-xl animate-bounce-gentle" style={{animationDelay: '0.5s'}}></div>
      
      {/* Grid <PERSON> */}
      <div className="absolute inset-0 opacity-5">
        <div className="h-full w-full" style={{
          backgroundImage: `
            linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>
      
      {/* Radial Gradients */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-300/20 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-purple-300/20 to-transparent rounded-full blur-3xl"></div>
    </div>
  )
}

export default AnimatedBackground
