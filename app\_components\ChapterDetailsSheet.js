"use client"
import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  BookOpen, 
  PlayCircle,
  CheckCircle,
  FileText,
  Video,
  Download,
  Star
} from "lucide-react"

function ChapterDetailsSheet({ chapter, chapterIndex, isOpen, onOpenChange, children }) {
  if (!chapter) return children

  const sampleTopics = [
    "مقدمة في الموضوع",
    "المفاهيم الأساسية", 
    "التطبيق العملي",
    "أمثلة متقدمة",
    "التمارين والممارسة"
  ]

  const sampleResources = [
    { type: "video", title: "شرح مرئي للموضوع", duration: "15 دقيقة" },
    { type: "document", title: "ملف PDF للمراجعة", size: "2.5 MB" },
    { type: "exercise", title: "تمارين تطبيقية", questions: "10 أسئلة" },
    { type: "quiz", title: "اختبار الفصل", questions: "5 أسئلة" }
  ]

  const getResourceIcon = (type) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4 text-red-500" />
      case 'document': return <FileText className="w-4 h-4 text-blue-500" />
      case 'exercise': return <BookOpen className="w-4 h-4 text-green-500" />
      case 'quiz': return <CheckCircle className="w-4 h-4 text-purple-500" />
      default: return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      
      <SheetContent side="right" className="w-full sm:max-w-lg overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="text-right">
            الفصل {chapterIndex + 1}: {chapter.name || `الفصل ${chapterIndex + 1}`}
          </SheetTitle>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Chapter Overview */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">نظرة عامة</h3>
            <p className="text-blue-700 text-sm leading-relaxed">
              {chapter.description || "في هذا الفصل ستتعلم المفاهيم الأساسية والتطبيقات العملية للموضوع. سنغطي جميع النقاط المهمة مع أمثلة تفاعلية."}
            </p>
          </div>

          {/* Chapter Stats */}
          <div className="grid grid-cols-3 gap-3">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <Clock className="w-5 h-5 text-gray-600 mx-auto mb-1" />
              <p className="text-xs text-gray-600">المدة</p>
              <p className="font-semibold text-sm">{chapter.duration || "45 دقيقة"}</p>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <BookOpen className="w-5 h-5 text-gray-600 mx-auto mb-1" />
              <p className="text-xs text-gray-600">المواضيع</p>
              <p className="font-semibold text-sm">{chapter.topics?.length || sampleTopics.length}</p>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <Star className="w-5 h-5 text-gray-600 mx-auto mb-1" />
              <p className="text-xs text-gray-600">التقييم</p>
              <p className="font-semibold text-sm">4.8</p>
            </div>
          </div>

          {/* Topics List */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-blue-600" />
              المواضيع المغطاة
            </h3>
            <div className="space-y-2">
              {(chapter.topics || sampleTopics).map((topic, index) => (
                <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded-lg">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-semibold text-blue-600">{index + 1}</span>
                  </div>
                  <span className="text-sm text-gray-700">{typeof topic === 'string' ? topic : topic.name || topic}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Learning Resources */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <FileText className="w-5 h-5 text-green-600" />
              مصادر التعلم
            </h3>
            <div className="space-y-3">
              {sampleResources.map((resource, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                  {getResourceIcon(resource.type)}
                  <div className="flex-1">
                    <h4 className="font-medium text-sm text-gray-800">{resource.title}</h4>
                    <p className="text-xs text-gray-500">
                      {resource.duration || resource.size || `${resource.questions}`}
                    </p>
                  </div>
                  <Button size="sm" variant="ghost" className="text-blue-600 hover:text-blue-700">
                    <PlayCircle className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Prerequisites */}
          <div>
            <h3 className="font-semibold mb-3">المتطلبات المسبقة</h3>
            <div className="space-y-2">
              <Badge variant="outline" className="text-xs">
                إكمال الفصل السابق
              </Badge>
              {chapterIndex > 0 && (
                <Badge variant="outline" className="text-xs">
                  فهم المفاهيم الأساسية
                </Badge>
              )}
            </div>
          </div>

          {/* Learning Objectives */}
          <div>
            <h3 className="font-semibold mb-3">أهداف التعلم</h3>
            <div className="space-y-2">
              {[
                "فهم المفاهيم الأساسية للموضوع",
                "تطبيق المعرفة النظرية عملياً", 
                "حل المشاكل المتعلقة بالموضوع",
                "التحضير للفصل التالي"
              ].map((objective, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{objective}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4 border-t">
            <Button className="w-full" size="lg">
              <PlayCircle className="w-4 h-4 mr-2" />
              بدء الفصل
            </Button>
            
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-1" />
                تحميل المواد
              </Button>
              <Button variant="outline" size="sm">
                <BookOpen className="w-4 h-4 mr-1" />
                معاينة
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default ChapterDetailsSheet
