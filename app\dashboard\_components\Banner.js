import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  <PERSON>rk<PERSON>,
  TrendingUp,
  Users,
  BookOpen,
  Award,
  ArrowRight,
  Star
} from 'lucide-react'

function Banner() {
  return (
    <div className="relative bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 rounded-2xl overflow-hidden shadow-2xl">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-4 left-4 w-16 h-16 border-2 border-white rounded-full"></div>
        <div className="absolute top-12 right-8 w-8 h-8 border border-white rounded-full"></div>
        <div className="absolute bottom-8 left-12 w-12 h-12 border border-white rounded-full"></div>
        <div className="absolute bottom-4 right-4 w-20 h-20 border-2 border-white rounded-full"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-6 right-6 animate-bounce-gentle">
        <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
          <Sparkles className="w-6 h-6 text-white" />
        </div>
      </div>

      <div className="absolute bottom-6 left-6 floating">
        <div className="w-10 h-10 bg-yellow-400/30 rounded-full flex items-center justify-center">
          <Award className="w-5 h-5 text-white" />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-8">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between">
          <div className="flex-1 mb-6 lg:mb-0">
            {/* Welcome Badge */}
            <Badge className="bg-white/20 text-white border-white/30 mb-4 animate-fade-in">
              <Star className="w-3 h-3 mr-1" />
              مرحباً بك في منصة التعلم الذكية
            </Badge>

            {/* Main Title */}
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight animate-slide-up">
              ابدأ رحلتك التعليمية
              <span className="block text-yellow-300">مع الذكاء الاصطناعي</span>
            </h1>

            {/* Subtitle */}
            <p className="text-blue-100 text-lg lg:text-xl mb-6 max-w-2xl animate-slide-up" style={{animationDelay: '0.2s'}}>
              تعلم أكثر، أنفق أقل، وشارك المعرفة مع أصدقائك.
              منصة تعليمية متطورة تستخدم أحدث تقنيات الذكاء الاصطناعي.
            </p>

            {/* Stats */}
            <div className="flex flex-wrap gap-6 mb-6 animate-slide-up" style={{animationDelay: '0.4s'}}>
              <div className="flex items-center gap-2 text-white">
                <Users className="w-5 h-5 text-yellow-300" />
                <span className="font-semibold">10,000+ طالب</span>
              </div>
              <div className="flex items-center gap-2 text-white">
                <BookOpen className="w-5 h-5 text-yellow-300" />
                <span className="font-semibold">50+ دورة</span>
              </div>
              <div className="flex items-center gap-2 text-white">
                <TrendingUp className="w-5 h-5 text-yellow-300" />
                <span className="font-semibold">95% نجاح</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 animate-scale-in" style={{animationDelay: '0.6s'}}>
              <Button className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-3 rounded-full shadow-lg hover-lift">
                <Sparkles className="w-4 h-4 mr-2" />
                إنشاء دورة جديدة
              </Button>

              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-full">
                استكشف الدورات
                <ArrowRight className="w-4 h-4 mr-2" />
              </Button>
            </div>
          </div>

          {/* Side Illustration */}
          <div className="hidden lg:block relative">
            <div className="w-64 h-64 relative">
              {/* Animated Circles */}
              <div className="absolute inset-0 animate-spin" style={{animationDuration: '20s'}}>
                <div className="w-full h-full border-2 border-white/30 rounded-full"></div>
              </div>
              <div className="absolute inset-4 animate-spin" style={{animationDuration: '15s', animationDirection: 'reverse'}}>
                <div className="w-full h-full border border-white/20 rounded-full"></div>
              </div>

              {/* Center Icon */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <BookOpen className="w-10 h-10 text-white" />
                </div>
              </div>

              {/* Floating Icons */}
              <div className="absolute top-4 left-4 w-8 h-8 bg-yellow-400/80 rounded-full flex items-center justify-center animate-bounce-gentle">
                <Star className="w-4 h-4 text-white" />
              </div>
              <div className="absolute top-4 right-4 w-8 h-8 bg-green-400/80 rounded-full flex items-center justify-center floating">
                <Award className="w-4 h-4 text-white" />
              </div>
              <div className="absolute bottom-4 left-4 w-8 h-8 bg-purple-400/80 rounded-full flex items-center justify-center floating" style={{animationDelay: '1s'}}>
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="absolute bottom-4 right-4 w-8 h-8 bg-pink-400/80 rounded-full flex items-center justify-center animate-bounce-gentle" style={{animationDelay: '0.5s'}}>
                <Users className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Banner