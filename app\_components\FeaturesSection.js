"use client"
import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { 
  Code, 
  Database, 
  Palette, 
  Brain, 
  Smartphone, 
  Globe,
  BookOpen,
  PlayCircle,
  Users,
  Award,
  Clock,
  Star
} from "lucide-react"
import CoursePreviewModal from './CoursePreviewModal'

const courseCategories = [
  {
    id: 1,
    title: "Web Development",
    icon: Code,
    description: "تعلم تطوير المواقع الحديثة باستخدام أحدث التقنيات",
    color: "from-blue-500 to-cyan-500",
    courses: [
      {
        name: "React.js للمبتدئين",
        chapters: 12,
        duration: "8 ساعات",
        level: "مبتدئ",
        topics: ["JSX", "Components", "State Management", "Hooks", "Routing"]
      },
      {
        name: "Node.js و Express",
        chapters: 10,
        duration: "6 ساعات", 
        level: "متوسط",
        topics: ["Server Setup", "APIs", "Database Integration", "Authentication", "Deployment"]
      },
      {
        name: "Next.js المتقدم",
        chapters: 15,
        duration: "12 ساعة",
        level: "متقدم",
        topics: ["SSR", "SSG", "API Routes", "Performance", "SEO"]
      }
    ]
  },
  {
    id: 2,
    title: "Data Science",
    icon: Database,
    description: "اكتشف عالم البيانات والذكاء الاصطناعي",
    color: "from-purple-500 to-pink-500",
    courses: [
      {
        name: "Python للبيانات",
        chapters: 14,
        duration: "10 ساعات",
        level: "مبتدئ",
        topics: ["Pandas", "NumPy", "Matplotlib", "Data Cleaning", "Analysis"]
      },
      {
        name: "Machine Learning",
        chapters: 18,
        duration: "15 ساعة",
        level: "متوسط",
        topics: ["Algorithms", "Supervised Learning", "Unsupervised Learning", "Neural Networks", "Model Evaluation"]
      },
      {
        name: "Deep Learning",
        chapters: 20,
        duration: "18 ساعة",
        level: "متقدم",
        topics: ["TensorFlow", "Keras", "CNN", "RNN", "Transfer Learning"]
      }
    ]
  },
  {
    id: 3,
    title: "UI/UX Design",
    icon: Palette,
    description: "صمم تجارب مستخدم استثنائية وواجهات جذابة",
    color: "from-green-500 to-teal-500",
    courses: [
      {
        name: "أساسيات التصميم",
        chapters: 8,
        duration: "5 ساعات",
        level: "مبتدئ",
        topics: ["Design Principles", "Color Theory", "Typography", "Layout", "Wireframing"]
      },
      {
        name: "Figma المتقدم",
        chapters: 12,
        duration: "8 ساعات",
        level: "متوسط",
        topics: ["Prototyping", "Components", "Auto Layout", "Plugins", "Collaboration"]
      },
      {
        name: "UX Research",
        chapters: 10,
        duration: "7 ساعات",
        level: "متوسط",
        topics: ["User Research", "Personas", "Journey Mapping", "Usability Testing", "Analytics"]
      }
    ]
  },
  {
    id: 4,
    title: "Mobile Development",
    icon: Smartphone,
    description: "طور تطبيقات الهاتف المحمول الحديثة",
    color: "from-orange-500 to-red-500",
    courses: [
      {
        name: "React Native",
        chapters: 16,
        duration: "12 ساعة",
        level: "متوسط",
        topics: ["Navigation", "State Management", "Native Modules", "Performance", "Publishing"]
      },
      {
        name: "Flutter للمبتدئين",
        chapters: 14,
        duration: "10 ساعات",
        level: "مبتدئ",
        topics: ["Dart", "Widgets", "State Management", "Animations", "Platform Integration"]
      }
    ]
  },
  {
    id: 5,
    title: "Artificial Intelligence",
    icon: Brain,
    description: "استكشف مستقبل التكنولوجيا مع الذكاء الاصطناعي",
    color: "from-indigo-500 to-purple-500",
    courses: [
      {
        name: "مقدمة في الذكاء الاصطناعي",
        chapters: 12,
        duration: "9 ساعات",
        level: "مبتدئ",
        topics: ["AI Fundamentals", "Problem Solving", "Search Algorithms", "Knowledge Representation", "Expert Systems"]
      },
      {
        name: "Natural Language Processing",
        chapters: 15,
        duration: "12 ساعة",
        level: "متقدم",
        topics: ["Text Processing", "Sentiment Analysis", "Language Models", "Chatbots", "Translation"]
      }
    ]
  },
  {
    id: 6,
    title: "DevOps & Cloud",
    icon: Globe,
    description: "تعلم نشر وإدارة التطبيقات في السحابة",
    color: "from-cyan-500 to-blue-500",
    courses: [
      {
        name: "Docker & Kubernetes",
        chapters: 13,
        duration: "10 ساعات",
        level: "متوسط",
        topics: ["Containerization", "Orchestration", "Scaling", "Monitoring", "Security"]
      },
      {
        name: "AWS للمطورين",
        chapters: 18,
        duration: "15 ساعة",
        level: "متوسط",
        topics: ["EC2", "S3", "Lambda", "RDS", "CloudFormation"]
      }
    ]
  }
]

function FeaturesSection() {
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleCategoryClick = (category) => {
    setSelectedCategory(category)
    setIsModalOpen(true)
  }

  return (
    <>
      <section id="features" className="py-20 px-6 max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-600 px-6 py-3 rounded-full mb-6">
            <span className="text-2xl">🎯</span>
            <span className="font-semibold">مجالات التعلم</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            استكشف عالم المعرفة
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            🚀 اختر من بين مجموعة واسعة من المجالات التقنية المتطورة
            <br />
            <span className="text-blue-600 font-semibold">واكتشف الدورات المصممة خصيصاً لك</span>
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {courseCategories.map((category) => {
            const IconComponent = category.icon
            return (
              <div
                key={category.id}
                onClick={() => handleCategoryClick(category)}
                className="group cursor-pointer p-8 rounded-3xl shadow-xl bg-white hover:shadow-2xl transform hover:scale-105 transition-all duration-500 border border-gray-100 hover:border-blue-200 relative overflow-hidden"
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

                {/* Icon Container */}
                <div className="relative">
                  <div className={`w-20 h-20 rounded-2xl bg-gradient-to-r ${category.color} flex items-center justify-center mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg`}>
                    <IconComponent className="w-10 h-10 text-white" />
                  </div>

                  {/* Floating Badge */}
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-xs font-bold text-gray-800 opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-0 group-hover:scale-100">
                    {category.courses.length}
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                  {category.title}
                </h3>

                <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                  {category.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-full">
                      <BookOpen className="w-4 h-4" />
                      <span className="font-medium">{category.courses.length} دورات</span>
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-white hover:bg-blue-600 transition-all duration-300 rounded-full px-4 py-2 group-hover:scale-110"
                  >
                    <span className="flex items-center gap-2">
                      استكشف <PlayCircle className="w-4 h-4" />
                    </span>
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Enhanced Statistics Section */}
        <div className="mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-800 mb-2">أرقام تتحدث عن نفسها</h3>
            <p className="text-gray-600">إنجازات حقيقية من مجتمع المتعلمين</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-gray-800 mb-1">10,000+</h3>
              <p className="text-gray-600 font-medium">طالب نشط</p>
              <div className="w-full bg-blue-100 rounded-full h-2 mt-3">
                <div className="bg-blue-500 h-2 rounded-full w-4/5"></div>
              </div>
            </div>

            <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-gray-800 mb-1">50+</h3>
              <p className="text-gray-600 font-medium">دورة تدريبية</p>
              <div className="w-full bg-green-100 rounded-full h-2 mt-3">
                <div className="bg-green-500 h-2 rounded-full w-3/4"></div>
              </div>
            </div>

            <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-gray-800 mb-1">95%</h3>
              <p className="text-gray-600 font-medium">معدل الرضا</p>
              <div className="w-full bg-yellow-100 rounded-full h-2 mt-3">
                <div className="bg-yellow-500 h-2 rounded-full w-full"></div>
              </div>
            </div>

            <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Clock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-gray-800 mb-1">24/7</h3>
              <p className="text-gray-600 font-medium">دعم متواصل</p>
              <div className="w-full bg-purple-100 rounded-full h-2 mt-3">
                <div className="bg-purple-500 h-2 rounded-full w-5/6"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <CoursePreviewModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        category={selectedCategory}
      />
    </>
  )
}

export default FeaturesSection
