"use client"
import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { 
  Code, 
  Database, 
  Palette, 
  Brain, 
  Smartphone, 
  Globe,
  BookOpen,
  PlayCircle,
  Users,
  Award,
  Clock,
  Star
} from "lucide-react"
import CoursePreviewModal from './CoursePreviewModal'

const courseCategories = [
  {
    id: 1,
    title: "Web Development",
    icon: Code,
    description: "تعلم تطوير المواقع الحديثة باستخدام أحدث التقنيات",
    color: "from-blue-500 to-cyan-500",
    courses: [
      {
        name: "React.js للمبتدئين",
        chapters: 12,
        duration: "8 ساعات",
        level: "مبتدئ",
        topics: ["JSX", "Components", "State Management", "Hooks", "Routing"]
      },
      {
        name: "Node.js و Express",
        chapters: 10,
        duration: "6 ساعات", 
        level: "متوسط",
        topics: ["Server Setup", "APIs", "Database Integration", "Authentication", "Deployment"]
      },
      {
        name: "Next.js المتقدم",
        chapters: 15,
        duration: "12 ساعة",
        level: "متقدم",
        topics: ["SSR", "SSG", "API Routes", "Performance", "SEO"]
      }
    ]
  },
  {
    id: 2,
    title: "Data Science",
    icon: Database,
    description: "اكتشف عالم البيانات والذكاء الاصطناعي",
    color: "from-purple-500 to-pink-500",
    courses: [
      {
        name: "Python للبيانات",
        chapters: 14,
        duration: "10 ساعات",
        level: "مبتدئ",
        topics: ["Pandas", "NumPy", "Matplotlib", "Data Cleaning", "Analysis"]
      },
      {
        name: "Machine Learning",
        chapters: 18,
        duration: "15 ساعة",
        level: "متوسط",
        topics: ["Algorithms", "Supervised Learning", "Unsupervised Learning", "Neural Networks", "Model Evaluation"]
      },
      {
        name: "Deep Learning",
        chapters: 20,
        duration: "18 ساعة",
        level: "متقدم",
        topics: ["TensorFlow", "Keras", "CNN", "RNN", "Transfer Learning"]
      }
    ]
  },
  {
    id: 3,
    title: "UI/UX Design",
    icon: Palette,
    description: "صمم تجارب مستخدم استثنائية وواجهات جذابة",
    color: "from-green-500 to-teal-500",
    courses: [
      {
        name: "أساسيات التصميم",
        chapters: 8,
        duration: "5 ساعات",
        level: "مبتدئ",
        topics: ["Design Principles", "Color Theory", "Typography", "Layout", "Wireframing"]
      },
      {
        name: "Figma المتقدم",
        chapters: 12,
        duration: "8 ساعات",
        level: "متوسط",
        topics: ["Prototyping", "Components", "Auto Layout", "Plugins", "Collaboration"]
      },
      {
        name: "UX Research",
        chapters: 10,
        duration: "7 ساعات",
        level: "متوسط",
        topics: ["User Research", "Personas", "Journey Mapping", "Usability Testing", "Analytics"]
      }
    ]
  },
  {
    id: 4,
    title: "Mobile Development",
    icon: Smartphone,
    description: "طور تطبيقات الهاتف المحمول الحديثة",
    color: "from-orange-500 to-red-500",
    courses: [
      {
        name: "React Native",
        chapters: 16,
        duration: "12 ساعة",
        level: "متوسط",
        topics: ["Navigation", "State Management", "Native Modules", "Performance", "Publishing"]
      },
      {
        name: "Flutter للمبتدئين",
        chapters: 14,
        duration: "10 ساعات",
        level: "مبتدئ",
        topics: ["Dart", "Widgets", "State Management", "Animations", "Platform Integration"]
      }
    ]
  },
  {
    id: 5,
    title: "Artificial Intelligence",
    icon: Brain,
    description: "استكشف مستقبل التكنولوجيا مع الذكاء الاصطناعي",
    color: "from-indigo-500 to-purple-500",
    courses: [
      {
        name: "مقدمة في الذكاء الاصطناعي",
        chapters: 12,
        duration: "9 ساعات",
        level: "مبتدئ",
        topics: ["AI Fundamentals", "Problem Solving", "Search Algorithms", "Knowledge Representation", "Expert Systems"]
      },
      {
        name: "Natural Language Processing",
        chapters: 15,
        duration: "12 ساعة",
        level: "متقدم",
        topics: ["Text Processing", "Sentiment Analysis", "Language Models", "Chatbots", "Translation"]
      }
    ]
  },
  {
    id: 6,
    title: "DevOps & Cloud",
    icon: Globe,
    description: "تعلم نشر وإدارة التطبيقات في السحابة",
    color: "from-cyan-500 to-blue-500",
    courses: [
      {
        name: "Docker & Kubernetes",
        chapters: 13,
        duration: "10 ساعات",
        level: "متوسط",
        topics: ["Containerization", "Orchestration", "Scaling", "Monitoring", "Security"]
      },
      {
        name: "AWS للمطورين",
        chapters: 18,
        duration: "15 ساعة",
        level: "متوسط",
        topics: ["EC2", "S3", "Lambda", "RDS", "CloudFormation"]
      }
    ]
  }
]

function FeaturesSection() {
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleCategoryClick = (category) => {
    setSelectedCategory(category)
    setIsModalOpen(true)
  }

  return (
    <>
      <section id="features" className="py-16 px-6 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-blue-600 mb-4">استكشف مجالات التعلم</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            اختر من بين مجموعة واسعة من المجالات التقنية واكتشف الدورات المتاحة في كل مجال
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {courseCategories.map((category) => {
            const IconComponent = category.icon
            return (
              <div
                key={category.id}
                onClick={() => handleCategoryClick(category)}
                className="group cursor-pointer p-6 rounded-2xl shadow-xl bg-white hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100"
              >
                <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${category.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                  {category.title}
                </h3>
                
                <p className="text-gray-600 mb-4 line-clamp-2">
                  {category.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <BookOpen className="w-4 h-4" />
                    <span>{category.courses.length} دورات</span>
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  >
                    استكشف <PlayCircle className="w-4 h-4 mr-1" />
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Statistics Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="p-4">
            <div className="flex items-center justify-center mb-2">
              <Users className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800">10,000+</h3>
            <p className="text-gray-600">طالب نشط</p>
          </div>
          
          <div className="p-4">
            <div className="flex items-center justify-center mb-2">
              <BookOpen className="w-8 h-8 text-green-500" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800">50+</h3>
            <p className="text-gray-600">دورة تدريبية</p>
          </div>
          
          <div className="p-4">
            <div className="flex items-center justify-center mb-2">
              <Award className="w-8 h-8 text-yellow-500" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800">95%</h3>
            <p className="text-gray-600">معدل الرضا</p>
          </div>
          
          <div className="p-4">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-8 h-8 text-purple-500" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800">24/7</h3>
            <p className="text-gray-600">دعم متواصل</p>
          </div>
        </div>
      </section>

      <CoursePreviewModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        category={selectedCategory}
      />
    </>
  )
}

export default FeaturesSection
