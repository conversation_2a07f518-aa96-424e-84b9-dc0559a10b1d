import { NextResponse, type NextRequest } from "next/server";
import { clerkClient } from "@clerk/nextjs/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { usersTable, userRoleEnum } from "@/lib/schema";
import { getSelf } from "@/lib/actions/user";
import { eq } from "drizzle-orm";

// مخطط للتحقق من أن الدور المرسل صالح
const updateRoleSchema = z.object({
  role: z.enum(userRoleEnum.enumValues),
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // 1. التحقق الأمني: هل المستخدم الذي يستدعي الواجهة هو مسؤول؟
    const self = await getSelf();
    if (self?.role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: Access denied." }, { status: 403 });
    }

    // 2. الحصول على معرّف المستخدم المستهدف والتحقق من الدور الجديد
    const targetClerkId = params.userId;
    const body = await request.json();
    const validation = updateRoleSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: "Invalid input", details: validation.error.flatten() }, { status: 400 });
    }
    const { role: newRole } = validation.data;

    // 3. إجراء أمان إضافي: منع المسؤول من تغيير دوره بنفسه
    if (self.clerkUserId === targetClerkId) {
        return NextResponse.json({ error: "Admins cannot change their own role." }, { status: 400 });
    }

    // 4. تحديث البيانات الوصفية (Metadata) في Clerk (المصدر الأساسي للحقيقة)
    await clerkClient.users.updateUserMetadata(targetClerkId, {
      publicMetadata: {
        role: newRole,
      },
    });

    // 5. تحديث الدور في قاعدة البيانات المحلية
    const updatedUser = await db
      .update(usersTable)
      .set({ role: newRole })
      .where(eq(usersTable.clerkUserId, targetClerkId))
      .returning();

    return NextResponse.json(updatedUser[0], { status: 200 });

  } catch (error) {
    console.error("Error updating user role:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}