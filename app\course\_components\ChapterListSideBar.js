import React, { useContext } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { SelectedChaptersContext } from '@/context/SelectedChapter'
import {
  PlayCircle,
  CheckCircle,
  Clock,
  BookOpen,
  Lock,
  Star,
  FileText
} from "lucide-react";
function ChapterListSideBar({courseInfo}) {
  const courses = courseInfo?.courses;
  const enrolToCourse = courseInfo?.enrolToCourse;
  const courseContent= courseInfo?.courses?.courseContent;

    const {selectedChapters, setSelectedChapters} = useContext(SelectedChaptersContext)

  const getChapterStatus = (index) => {
    // Mock completion status - in real app this would come from user progress
    if (index < selectedChapters) return 'completed'
    if (index === selectedChapters) return 'current'
    return 'locked'
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'current': return <PlayCircle className="w-4 h-4 text-blue-500" />
      case 'locked': return <Lock className="w-4 h-4 text-gray-400" />
      default: return <BookOpen className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'border-l-green-500 bg-green-50'
      case 'current': return 'border-l-blue-500 bg-blue-50'
      case 'locked': return 'border-l-gray-300 bg-gray-50'
      default: return 'border-l-gray-300 bg-white'
    }
  }

  return (
    <div className='h-screen w-80 bg-gradient-to-b from-gray-50 to-gray-100 border-r border-gray-200'>
      {/* Header */}
      <div className='p-6 bg-white border-b border-gray-200'>
        <div className="flex items-center gap-3 mb-2">
          <BookOpen className="w-6 h-6 text-blue-600" />
          <h2 className='text-xl font-bold text-gray-800'>فصول الدورة</h2>
        </div>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>{courseContent?.length || 0} فصل</span>
          <span>•</span>
          <span>8 ساعات</span>
          <span>•</span>
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-500" />
            <span>4.8</span>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="p-4 bg-white border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">التقدم</span>
          <span className="text-sm text-gray-600">
            {Math.round(((selectedChapters + 1) / (courseContent?.length || 1)) * 100)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((selectedChapters + 1) / (courseContent?.length || 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Chapters List */}
      <div className="overflow-y-auto flex-1">
        <Accordion type="single" collapsible className="p-2">
          {courseContent?.map((chapter, index) => {
            const status = getChapterStatus(index)
            const isActive = index === selectedChapters

            return (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className={`border-l-4 mb-2 rounded-lg overflow-hidden ${getStatusColor(status)} ${
                  isActive ? 'ring-2 ring-blue-200' : ''
                }`}
              >
                <AccordionTrigger
                  onClick={() => setSelectedChapters(index)}
                  className={`font-semibold p-4 cursor-pointer text-right hover:no-underline ${
                    isActive ? 'text-blue-700' : 'text-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-3 w-full">
                    {getStatusIcon(status)}
                    <div className="flex-1 text-right">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          الفصل {index + 1}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          45 دقيقة
                        </Badge>
                      </div>
                      <h3 className="text-base font-semibold mt-1">
                        {chapter?.courseData?.[0]?.chapterName || `الفصل ${index + 1}`}
                      </h3>
                    </div>
                  </div>
                </AccordionTrigger>

                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-3">
                    {/* Chapter Topic */}
                    <div className="bg-white p-3 rounded-lg border border-gray-200">
                      <h4 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
                        <FileText className="w-4 h-4 text-blue-500" />
                        الموضوع الرئيسي
                      </h4>
                      <p className="text-sm text-gray-600">
                        {chapter?.courseData?.[0]?.topic || 'موضوع الفصل'}
                      </p>
                    </div>

                    {/* Chapter Stats */}
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-white p-2 rounded-lg border border-gray-200 text-center">
                        <Clock className="w-4 h-4 text-gray-500 mx-auto mb-1" />
                        <p className="text-xs text-gray-600">45 دقيقة</p>
                      </div>
                      <div className="bg-white p-2 rounded-lg border border-gray-200 text-center">
                        <BookOpen className="w-4 h-4 text-gray-500 mx-auto mb-1" />
                        <p className="text-xs text-gray-600">5 مواضيع</p>
                      </div>
                    </div>

                    {/* Action Button */}
                    {status !== 'locked' && (
                      <Button
                        size="sm"
                        className="w-full"
                        variant={status === 'completed' ? 'outline' : 'default'}
                        onClick={() => setSelectedChapters(index)}
                      >
                        {status === 'completed' ? (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            مراجعة
                          </>
                        ) : (
                          <>
                            <PlayCircle className="w-4 h-4 mr-2" />
                            {status === 'current' ? 'متابعة' : 'بدء'}
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )
          })}
        </Accordion>
      </div>
    </div>
  )
}

export default ChapterListSideBar