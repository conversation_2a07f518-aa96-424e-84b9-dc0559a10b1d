import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { usersTable } from "@/lib/schema";
import { getSelf } from "@/lib/actions/user";
import { count, gte, sql } from "drizzle-orm";

export async function GET() {
  try {
    // 1. التحقق الأمني: هل المستخدم الذي يستدعي الواجهة هو مسؤول؟
    const self = await getSelf();
    if (self?.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // 2. حساب تاريخ بداية الأسبوع (قبل 7 أيام)
    const sevenDaysAgo = new Date();
    const thirtyDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // 3. جلب الإحصائيات من قاعدة البيانات
    const [totalUsersResult, newUsersResult, dailyGrowthResult] = await Promise.all([
      db.select({ value: count() }).from(usersTable),
      db.select({ value: count() }).from(usersTable).where(gte(usersTable.createdAt, sevenDaysAgo)),
      // استعلام جديد لجلب عدد المستخدمين الجدد لكل يوم في آخر 30 يومًا
      db.select({
          date: sql<string>`TO_CHAR(${usersTable.createdAt}, 'YYYY-MM-DD')`,
          count: sql<number>`count(*)`.mapWith(Number),
      })
      .from(usersTable)
      .where(gte(usersTable.createdAt, thirtyDaysAgo))
      .groupBy(sql`TO_CHAR(${usersTable.createdAt}, 'YYYY-MM-DD')`)
      .orderBy(sql`date ASC`),
    ]);

    // 4. معالجة بيانات النمو لملء الأيام التي لا يوجد بها تسجيل
    const growthDataMap = new Map(dailyGrowthResult.map(item => [item.date, item.count]));
    const userGrowthLast30Days = [];
    for (let i = 29; i >= 0; i--) {
      const d = new Date();
      d.setDate(d.getDate() - i);
      const formattedDate = d.toISOString().split('T')[0];
      userGrowthLast30Days.push({
        date: formattedDate,
        count: growthDataMap.get(formattedDate) || 0,
      });
    }

    const stats = {
      totalUsers: totalUsersResult[0].value,
      newUsersThisWeek: newUsersResult[0].value,
      userGrowthLast30Days: userGrowthLast30Days,
    };

    return NextResponse.json(stats, { status: 200 });
  } catch (error) {
    console.error("Error fetching admin analytics:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}