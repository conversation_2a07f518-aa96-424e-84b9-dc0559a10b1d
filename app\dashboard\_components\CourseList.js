"use client"
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import CourseFormDialog from './CourseFormDialog'
import axios from 'axios'
import { useUser } from '@clerk/nextjs'
import CourseCard from './CourseCard'

function CourseList() {

    const [courseList,setCourseList] = useState([])
    const {user} = useUser()

        useEffect(()=>{
          user&&getCourseList()
        },[user])
    const getCourseList= async()=>{
        const result = await axios.get("/api/courses")
        console.log(result.data)
        setCourseList(result.data)
    }

    
  return (
    <div>
        <h2 className='p-3 mt-5 font-bold text-[34px] border-b-4 border-blue-400 inline-block text-blue-500'>Course List</h2>

        {courseList?.length==0 ?
        <div className='flex flex-col items-center py-12'>
            <div className="w-32 h-32 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <Image src={"/logo.png"} width={80} height={80} alt="Logo"/>
            </div>
            <h1 className='font-bold text-2xl text-gray-800 mb-2'>لا توجد دورات بعد</h1>
            <p className='text-gray-600 mb-6 text-center max-w-md'>
                ابدأ رحلتك التعليمية بإنشاء دورتك الأولى باستخدام الذكاء الاصطناعي
            </p>
            <CourseFormDialog>
                <Button className="mt-3 cursor-pointer bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-full">
                    + إنشاء دورة جديدة
                </Button>
            </CourseFormDialog>
        </div> :
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {courseList?.map((course, index)=>(
                <CourseCard key={course.cid || index} course={course}/>
            ))}
        </div>
        }
    </div>
  )
}

export default CourseList