import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";
import JSON5 from 'json5';

export async function POST(req){
    const {courseJson,name,courseId} = await  req.json()


const PROMPT = `You are an expert course content generator specialized in creating educational HTML content.

Generate HTML-formatted content for each topic based on the course name, category, and chapter.

Only return a **valid JSON array**. Each object must look like:
{
  "chapterName": "string",
  "topic": "string",
  "content": "string (HTML-formatted content)"
}

No extra text. No explanations. No code block markers. Escape all characters properly.

User Input:
`;

 const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
const promises = courseJson?.allchapters?.map(async(chapter)=>{
  console.log("🚀 chapter sent to model:\n", JSON.stringify(chapter, null, 2));
console.log("🧾 Full courseJson:", JSON.stringify(courseJson, null, 2));

    
const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    responseMimeType: 'text/plain',
  };
  const model = 'gemini-2.5-flash-preview-05-20';
  const contents = [
    {
      role: 'user',
      parts: [
        {
         text: PROMPT + JSON.stringify({
  courseName: name,
  category: courseJson.course.category,
  chapterName: chapter.chapterName,
  topics: chapter.topics
}),
        },
      ],
    },
  ];

  const response = await ai.models.generateContent({
    model,
    config,
    contents,
  });

  console.log(response.candidates[0].content.parts[0].text)
  const rowRES = response?.candidates[0]?.content?.parts[0]?.text
  const rowJSON = rowRES.replace('```json','').replace('```','')
  let ResJson;
try {
  ResJson = JSON5.parse(rowJSON);
} catch (e) {
  console.error("❌ JSON parsing error:", e.message);
  console.log("Offending content:\n", rowJSON);
}
  

   return ResJson.map((item) => ({
    chapterName: chapter.chapterName,
    topic: item.topic,
    content: item.content,
  }));
    
})

const courseContent = await Promise.all(promises)

return NextResponse.json({courseContent:courseContent,name:name})
}