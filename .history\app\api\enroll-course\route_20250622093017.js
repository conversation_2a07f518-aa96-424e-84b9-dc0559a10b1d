import { db } from "@/config/db"
import { enrollToCourseTable } from "@/config/schema"
import { currentUser } from "@clerk/nextjs/server"

export async function POST(req) {
    const   {courseId} = await req.json()

    const {user} = currentUser


    // if already enrolled

    const enrollCourse= await db.select().from(enrollToCourseTable).where(and(eq(enrollToCourseTable.email,user?.primaryEmailAddress.emailAddress),eq(enrollToCourseTable.cid,courseId)))

        if(enrollCourse?.length==0){
            const result = await db.insert(enrollToCourseTable).values({
                cid:courseId,
                email:user?.primaryEmailAddress?.emailAddress
            }).returning(enrollToCourseTable)

            return NextResponse.json(result)
        }

         return NextResponse.json({"already enrolled"})


}