"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import axios from 'axios';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from "sonner";

// ShadCN UI components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from '@/components/ui/input';
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from '@/components/ui/button';

// 1. تعريف مخطط Zod للتحقق من صحة المدخلات
const courseFormSchema = z.object({
  name: z.string().min(3, { message: "يجب أن يكون اسم الدورة 3 أحرف على الأقل." }),
  description: z.string().min(10, { message: "يجب أن يكون الوصف 10 أحرف على الأقل." }),
  level: z.enum(["Easy", "Moderate", "Hard"], {
    required_error: "الرجاء اختيار مستوى الصعوبة.",
  }),
  category: z.string().min(2, { message: "الفئة مطلوبة." }),
  chapters: z.coerce.number().min(1, { message: "يجب أن يكون هناك فصل واحد على الأقل." }),
  includeVideo: z.boolean().default(false),
});

// استنتاج نوع TypeScript من المخطط
type CourseFormValues = z.infer<typeof courseFormSchema>;

interface CourseFormDialogProps {
  children: React.ReactNode;
}

function CourseFormDialog({ children }: CourseFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [open, setOpen] = useState(false); // للتحكم في حالة الحوار
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // 2. إعداد react-hook-form
  const form = useForm<CourseFormValues>({
    resolver: zodResolver(courseFormSchema),
    defaultValues: {
      name: "",
      description: "",
      category: "",
      chapters: 1,
      includeVideo: false,
    },
  });

  // عند فتح الحوار، قم بالتركيز على أول حقل
  useEffect(() => {
    if (open) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [open]);

  // 3. تعريف معالج الإرسال
  const onSubmit = async (values: CourseFormValues) => {
    setIsSubmitting(true);
    try {
      // نفترض أن الـ API يعيد كائن الدورة مع حقل 'id'
      const result = await axios.post<{ id: string }>("/api/generete-course", values);
      console.log("Course generated:", result.data);
      toast.success("تم إنشاء الدورة بنجاح!");

      setOpen(false); // إغلاق الحوار عند النجاح
      form.reset(); // إعادة تعيين النموذج للاستخدام التالي

      // إعادة توجيه المستخدم إلى صفحة تعديل الدورة الجديدة
      router.push(`/dashboard/edit-course/${result.data.id}`);
    } catch (error) {
      console.error("Error generating course:", error);
      toast.error("حدث خطأ أثناء إنشاء الدورة. الرجاء المحاولة مرة أخرى.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>إنشاء دورة بالذكاء الاصطناعي</DialogTitle>
          <DialogDescription>
            املأ التفاصيل أدناه لإنشاء دورة جديدة.
          </DialogDescription>
        </DialogHeader>
        
        {/* 4. استخدام مكونات Form من ShadCN */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>اسم الدورة</FormLabel>
                  <FormControl>
                    <Input placeholder="مثال: مقدمة في React" {...field} ref={inputRef} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>وصف الدورة</FormLabel>
                  <FormControl>
                    <Textarea placeholder="ملخص موجز لمحتوى الدورة" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="chapters"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>عدد الفصول</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="level"
                render={({ field }) => (
                    <FormItem>
                    <FormLabel>المستوى</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                        <SelectTrigger><SelectValue placeholder="اختر المستوى" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                        <SelectItem value="Easy">سهل</SelectItem>
                        <SelectItem value="Moderate">متوسط</SelectItem>
                        <SelectItem value="Hard">صعب</SelectItem>
                        </SelectContent>
                    </Select>
                    <FormMessage />
                    </FormItem>
                )}
                />
            </div>

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الفئة</FormLabel>
                  <FormControl>
                    <Input placeholder="مثال: تطوير الويب" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="includeVideo"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>تضمين فيديوهات</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      aria-readonly
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              إنشاء الدورة
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default CourseFormDialog;